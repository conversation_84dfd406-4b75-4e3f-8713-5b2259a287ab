/**
 * Fuio.tech API 服务
 * 支持文字生图和图文生图功能
 */

// API配置
const FUIO_CONFIG = {
  baseUrl: 'https://api.fuio.tech',
  apiKey: 'sk-NE03sGi4hBFpbUwgidmBPNZonT3SjvaAQ3eG45s4idAHWa5i',
  model: 'sora-img', // 原始模型名称
  // 备用模型名称，如果 sora-img 不可用可以尝试这些
  alternativeModels: ['gpt-image-1', 'dall-e-3', 'stable-diffusion', 'midjourney'],
  timeout: 60000
}

// 图片尺寸配置
const IMAGE_SIZES = {
  '1:1': { width: 480, height: 480 },
  '2:3': { width: 480, height: 720 },
  '3:2': { width: 720, height: 480 }
}

/**
 * 将图片文件转换为base64
 */
async function fileToBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      // 移除data:image/...;base64,前缀
      const base64 = reader.result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = reject
    reader.readAsDataURL(file)
  })
}

/**
 * 发送请求到Fuio API，支持模型回退机制
 */
async function makeRequest(endpoint, data, retryWithAlternativeModel = true) {
  const url = `${FUIO_CONFIG.baseUrl}${endpoint}`

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${FUIO_CONFIG.apiKey}`
      },
      body: JSON.stringify(data),
      signal: AbortSignal.timeout(FUIO_CONFIG.timeout)
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      const errorMessage = errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`

      // 检查是否是模型不可用的错误
      if (retryWithAlternativeModel &&
          (errorMessage.includes('无可用资源') ||
           errorMessage.includes('model not available') ||
           errorMessage.includes('gpt-image-1'))) {

        console.warn('⚠️ 当前模型不可用，尝试使用备用模型:', errorMessage)

        // 尝试使用备用模型
        for (const altModel of FUIO_CONFIG.alternativeModels) {
          try {
            console.log('🔄 尝试备用模型:', altModel)
            const altData = { ...data, model: altModel }
            return await makeRequest(endpoint, altData, false) // 避免无限递归
          } catch (altError) {
            console.warn(`❌ 备用模型 ${altModel} 也失败:`, altError.message)
            continue
          }
        }
      }

      throw new Error(errorMessage)
    }

    return await response.json()
  } catch (error) {
    if (error.name === 'AbortError') {
      throw new Error('请求超时，请稍后重试')
    }
    throw error
  }
}

/**
 * 文字生图
 * @param {string} prompt - 文字描述
 * @param {Object} options - 生成选项
 */
export async function generateTextToImage(prompt, options = {}) {
  try {
    if (!prompt || typeof prompt !== 'string' || !prompt.trim()) {
      throw new Error('请提供有效的图像描述')
    }

    // 解析尺寸
    const aspectRatio = options.aspectRatio || '1:1'
    const size = IMAGE_SIZES[aspectRatio] || IMAGE_SIZES['1:1']

    const requestData = {
      model: FUIO_CONFIG.model,
      prompt: prompt.trim(),
      n: options.n || 1,
      size: `${size.width}x${size.height}`,
      response_format: 'url'
    }

    console.log('🎨 发送文字生图请求:', requestData)

    const response = await makeRequest('/v1/images/generations', requestData)

    if (response.data && response.data.length > 0) {
      const images = response.data.map((item, index) => ({
        url: item.url,
        index: index
      }))

      return {
        success: true,
        images: images,
        prompt: prompt,
        model: FUIO_CONFIG.model,
        size: `${size.width}x${size.height}`,
        count: images.length
      }
    } else {
      throw new Error('API返回数据格式错误')
    }
  } catch (error) {
    console.error('❌ 文字生图失败:', error)
    return {
      success: false,
      error: error.message || '图像生成失败'
    }
  }
}

/**
 * 图文生图（图像编辑）
 * @param {string} prompt - 文字描述
 * @param {File|string} image - 图片文件或base64字符串
 * @param {Object} options - 生成选项
 */
export async function generateImageToImage(prompt, image, options = {}) {
  try {
    if (!prompt || typeof prompt !== 'string' || !prompt.trim()) {
      throw new Error('请提供有效的图像描述')
    }

    if (!image) {
      throw new Error('请提供参考图像')
    }

    // 处理图像数据
    let imageBase64
    if (typeof image === 'string') {
      // 如果是base64字符串，直接使用
      imageBase64 = image.startsWith('data:') ? image.split(',')[1] : image
    } else if (image instanceof File) {
      // 如果是文件，转换为base64
      imageBase64 = await fileToBase64(image)
    } else {
      throw new Error('不支持的图像格式')
    }

    // 解析尺寸
    const aspectRatio = options.aspectRatio || '1:1'
    const size = IMAGE_SIZES[aspectRatio] || IMAGE_SIZES['1:1']

    const requestData = {
      model: FUIO_CONFIG.model,
      prompt: prompt.trim(),
      image: imageBase64,
      n: options.n || 1,
      size: `${size.width}x${size.height}`,
      response_format: 'url'
    }

    console.log('🎨 发送图文生图请求:', {
      ...requestData,
      image: `[base64 data: ${imageBase64.length} chars]`
    })

    console.log('📋 使用的模型名称:', FUIO_CONFIG.model)

    const response = await makeRequest('/v1/images/edits', requestData)

    if (response.data && response.data.length > 0) {
      const images = response.data.map((item, index) => ({
        url: item.url,
        index: index
      }))

      return {
        success: true,
        images: images,
        prompt: prompt,
        model: FUIO_CONFIG.model,
        size: `${size.width}x${size.height}`,
        count: images.length,
        type: 'image-to-image'
      }
    } else {
      throw new Error('API返回数据格式错误')
    }
  } catch (error) {
    console.error('❌ 图文生图失败:', error)

    // 提供更友好的错误信息
    let friendlyError = error.message || '图像编辑失败'

    if (error.message?.includes('无可用资源')) {
      friendlyError = '当前模型资源不足，请稍后重试或联系管理员'
    } else if (error.message?.includes('gpt-image-1')) {
      friendlyError = '模型配置问题，已尝试使用备用模型，请稍后重试'
    } else if (error.message?.includes('timeout')) {
      friendlyError = '请求超时，图像处理时间较长，请稍后重试'
    }

    return {
      success: false,
      error: friendlyError,
      originalError: error.message,
      model: FUIO_CONFIG.model
    }
  }
}

/**
 * 获取支持的图片尺寸
 */
export function getSupportedSizes() {
  return Object.keys(IMAGE_SIZES).map(ratio => ({
    ratio,
    ...IMAGE_SIZES[ratio],
    label: `${ratio} (${IMAGE_SIZES[ratio].width}×${IMAGE_SIZES[ratio].height})`
  }))
}

/**
 * 验证API密钥
 */
export async function validateApiKey() {
  try {
    // 发送一个简单的请求来验证API密钥
    const response = await makeRequest('/v1/models', {})
    return {
      success: true,
      models: response.data || []
    }
  } catch (error) {
    return {
      success: false,
      error: error.message
    }
  }
}

export default {
  generateTextToImage,
  generateImageToImage,
  getSupportedSizes,
  validateApiKey,
  config: FUIO_CONFIG
}
