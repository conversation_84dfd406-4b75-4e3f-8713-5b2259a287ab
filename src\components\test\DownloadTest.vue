<template>
  <div class="download-test p-6">
    <h2 class="text-2xl font-bold mb-6">下载功能测试</h2>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- 测试图片1 -->
      <div class="test-card bg-white rounded-lg shadow-md p-4">
        <h3 class="text-lg font-semibold mb-3">测试图片 1 (同域)</h3>
        <img 
          :src="testImages.local" 
          alt="本地测试图片" 
          class="w-full h-48 object-cover rounded mb-3"
        />
        <div class="flex space-x-2">
          <button 
            @click="downloadTest(testImages.local, 'local-image.png')"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            下载
          </button>
          <button 
            @click="checkDownloadable(testImages.local)"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            检查可下载性
          </button>
        </div>
      </div>

      <!-- 测试图片2 -->
      <div class="test-card bg-white rounded-lg shadow-md p-4">
        <h3 class="text-lg font-semibold mb-3">测试图片 2 (跨域)</h3>
        <img 
          :src="testImages.external" 
          alt="外部测试图片" 
          class="w-full h-48 object-cover rounded mb-3"
        />
        <div class="flex space-x-2">
          <button 
            @click="downloadTest(testImages.external, 'external-image.png')"
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            下载
          </button>
          <button 
            @click="checkDownloadable(testImages.external)"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            检查可下载性
          </button>
        </div>
      </div>

      <!-- 测试图片3 -->
      <div class="test-card bg-white rounded-lg shadow-md p-4">
        <h3 class="text-lg font-semibold mb-3">测试图片 3 (Base64)</h3>
        <img 
          :src="testImages.base64" 
          alt="Base64测试图片" 
          class="w-full h-48 object-cover rounded mb-3"
        />
        <div class="flex space-x-2">
          <button 
            @click="downloadTest(testImages.base64, 'base64-image.png')"
            class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            下载
          </button>
          <button 
            @click="getImageInfo(testImages.base64)"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            获取信息
          </button>
        </div>
      </div>

      <!-- 批量下载测试 -->
      <div class="test-card bg-white rounded-lg shadow-md p-4">
        <h3 class="text-lg font-semibold mb-3">批量下载测试</h3>
        <div class="grid grid-cols-3 gap-2 mb-3">
          <img 
            v-for="(img, index) in batchImages" 
            :key="index"
            :src="img.url" 
            :alt="`批量图片 ${index + 1}`" 
            class="w-full h-20 object-cover rounded"
          />
        </div>
        <button 
          @click="downloadBatch"
          class="w-full px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
        >
          批量下载 ({{ batchImages.length }} 张)
        </button>
      </div>
    </div>

    <!-- 下载状态 -->
    <div v-if="downloadStatus" class="mt-6 p-4 bg-gray-100 rounded-lg">
      <h3 class="text-lg font-semibold mb-2">下载状态</h3>
      <pre class="text-sm">{{ JSON.stringify(downloadStatus, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { downloadImage, downloadMultipleImages, checkDownloadable as checkDownloadableUtil, getImageInfo as getImageInfoUtil } from '@/utils/download.js'

// 测试图片
const testImages = ref({
  local: '/src/assets/logo.png', // 本地图片
  external: 'https://picsum.photos/400/300?random=1', // 外部图片
  base64: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkJhc2U2NCBUZXN0PC90ZXh0Pgo8L3N2Zz4K'
})

// 批量下载测试图片
const batchImages = ref([
  { url: 'https://picsum.photos/200/200?random=2', filename: 'batch-1.jpg' },
  { url: 'https://picsum.photos/200/200?random=3', filename: 'batch-2.jpg' },
  { url: 'https://picsum.photos/200/200?random=4', filename: 'batch-3.jpg' }
])

const downloadStatus = ref(null)

// 下载测试
const downloadTest = async (url, filename) => {
  try {
    const result = await downloadImage(url, filename)
    downloadStatus.value = result
    console.log('下载结果:', result)
  } catch (error) {
    console.error('下载错误:', error)
    downloadStatus.value = { success: false, error: error.message }
  }
}

// 检查可下载性
const checkDownloadable = async (url) => {
  try {
    const result = await checkDownloadableUtil(url)
    ElMessage.info(`图片${result ? '可以' : '不能'}下载`)
    downloadStatus.value = { checkDownloadable: result, url }
  } catch (error) {
    ElMessage.error('检查失败')
    console.error('检查错误:', error)
  }
}

// 获取图片信息
const getImageInfo = async (url) => {
  try {
    const info = await getImageInfoUtil(url)
    ElMessage.success('图片信息获取成功')
    downloadStatus.value = { imageInfo: info, url }
    console.log('图片信息:', info)
  } catch (error) {
    ElMessage.error('获取图片信息失败')
    console.error('获取信息错误:', error)
  }
}

// 批量下载
const downloadBatch = async () => {
  try {
    const result = await downloadMultipleImages(batchImages.value)
    downloadStatus.value = result
    console.log('批量下载结果:', result)
  } catch (error) {
    console.error('批量下载错误:', error)
    downloadStatus.value = { success: false, error: error.message }
  }
}
</script>

<style lang="scss" scoped>
.download-test {
  max-width: 1200px;
  margin: 0 auto;
}

.test-card {
  transition: transform 0.2s ease-in-out;
  
  &:hover {
    transform: translateY(-2px);
  }
}

pre {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  max-height: 300px;
}
</style>
