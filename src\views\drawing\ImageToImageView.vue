<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
    <!-- 顶部导航 -->
    <div class="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-10">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center space-x-4">
            <router-link to="/drawing" class="flex items-center space-x-2 text-gray-600 hover:text-purple-600 transition-colors">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>返回文字生图</span>
            </router-link>
            <div class="h-6 w-px bg-gray-300"></div>
            <h1 class="text-xl font-bold text-gray-900">图文生图</h1>
          </div>
          <div class="flex items-center space-x-4">
            <div class="text-sm text-gray-600">
              剩余配额: <span class="font-semibold text-purple-600">{{ remainingQuota }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- 左侧：图片上传和设置 -->
        <div class="space-y-6">
          <!-- 图片上传区域 -->
          <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200/50">
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
              <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" class="text-white">
                  <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z" fill="currentColor"/>
                </svg>
              </div>
              <span>参考图像</span>
            </h3>
            
            <div 
              class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 hover:bg-blue-50 transition-all duration-200 cursor-pointer"
              @click="triggerImageUpload"
              @dragover.prevent
              @drop.prevent="handleDrop"
            >
              <input
                ref="imageInput"
                type="file"
                accept="image/*"
                @change="handleImageUpload"
                class="hidden"
              />
              <div v-if="!uploadedImagePreview">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none" class="mx-auto text-gray-400 mb-4">
                  <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z" fill="currentColor"/>
                </svg>
                <p class="text-gray-600 font-medium mb-2">点击上传或拖拽图片到此处</p>
                <p class="text-gray-400 text-sm">支持 JPG、PNG 格式，最大 10MB</p>
              </div>
              <div v-else class="relative">
                <img :src="uploadedImagePreview" alt="上传的图片" class="max-h-64 mx-auto rounded-lg shadow-md" />
                <button 
                  @click.stop="removeUploadedImage"
                  class="absolute top-2 right-2 w-8 h-8 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center text-white transition-colors shadow-lg"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- 编辑描述 -->
          <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200/50">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">编辑描述</h3>
            <textarea
              v-model="prompt"
              placeholder="描述您想要对图像进行的修改，比如：将背景改为夕阳西下的海滩，添加一些海鸥..."
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
              rows="4"
            ></textarea>
          </div>

          <!-- 生成设置 -->
          <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200/50">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">生成设置</h3>
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <label class="text-sm font-medium text-gray-700">图片尺寸</label>
                <select v-model="selectedSize" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <option value="1:1">1:1 (480×480)</option>
                  <option value="2:3">2:3 (480×720)</option>
                  <option value="3:2">3:2 (720×480)</option>
                </select>
              </div>
              
              <div class="flex items-center justify-between">
                <label class="text-sm font-medium text-gray-700">生成数量</label>
                <select v-model="generateCount" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                  <option :value="1">1张</option>
                  <option :value="2">2张</option>
                  <option :value="3">3张</option>
                  <option :value="4">4张</option>
                </select>
              </div>
            </div>
          </div>

          <!-- 生成按钮 -->
          <button
            @click="handleGenerate"
            :disabled="!canGenerate || loading"
            class="w-full px-6 py-4 bg-gradient-to-r from-blue-500 to-purple-500 text-white font-medium rounded-lg hover:from-blue-600 hover:to-purple-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center space-x-2"
          >
            <div v-if="loading" class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" fill="currentColor"/>
            </svg>
            <span>{{ loading ? '生成中...' : '开始编辑' }}</span>
          </button>
        </div>

        <!-- 右侧：生成结果 -->
        <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200/50">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">生成结果</h3>
          
          <div v-if="!generatedImages.length && !loading" class="text-center py-12">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" class="mx-auto text-gray-400 mb-4">
              <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <h4 class="text-lg font-medium text-gray-900 mb-2">准备开始编辑</h4>
            <p class="text-gray-500">上传图片并输入编辑描述，AI将为您创作全新的图像</p>
          </div>

          <div v-if="loading" class="text-center py-12">
            <div class="w-16 h-16 border-4 border-blue-200 border-t-blue-500 rounded-full animate-spin mx-auto mb-4"></div>
            <h4 class="text-lg font-medium text-gray-900 mb-2">AI正在创作中...</h4>
            <p class="text-gray-500">请稍候，这可能需要几分钟时间</p>
          </div>

          <div v-if="generatedImages.length" class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div v-for="(image, index) in generatedImages" :key="index" class="relative group">
              <img :src="image.url" :alt="`生成的图片 ${index + 1}`" class="w-full rounded-lg shadow-md" />
              <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center">
                <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2">
                  <button 
                    @click="downloadImage(image.url, index)"
                    class="w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-lg hover:bg-gray-50 transition-colors"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4M7 10l5 5 5-5M12 15V3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </button>
                  <button 
                    @click="saveToGallery(image, index)"
                    class="w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-lg hover:bg-gray-50 transition-colors"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { generateImageToImage } from '@/services/fuioApi.js'
import { useDrawingStore } from '@/stores/drawing'

// Stores
const drawingStore = useDrawingStore()

// 响应式数据
const imageInput = ref(null)
const uploadedImageFile = ref(null)
const uploadedImagePreview = ref('')
const prompt = ref('')
const selectedSize = ref('1:1')
const generateCount = ref(1)
const loading = ref(false)
const generatedImages = ref([])
const remainingQuota = ref(47)

// 计算属性
const canGenerate = computed(() => {
  return uploadedImageFile.value && prompt.value.trim() && !loading.value
})

// 方法
const triggerImageUpload = () => {
  imageInput.value?.click()
}

const handleImageUpload = (event) => {
  const file = event.target.files[0]
  if (!file) return
  processFile(file)
}

const handleDrop = (event) => {
  const file = event.dataTransfer.files[0]
  if (!file) return
  processFile(file)
}

const processFile = (file) => {
  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }
  
  // 验证文件大小 (10MB)
  if (file.size > 10 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过10MB')
    return
  }
  
  uploadedImageFile.value = file
  
  // 创建预览
  const reader = new FileReader()
  reader.onload = (e) => {
    uploadedImagePreview.value = e.target.result
  }
  reader.readAsDataURL(file)
}

const removeUploadedImage = () => {
  uploadedImageFile.value = null
  uploadedImagePreview.value = ''
  if (imageInput.value) {
    imageInput.value.value = ''
  }
}

const handleGenerate = async () => {
  if (!canGenerate.value) return
  
  try {
    loading.value = true
    generatedImages.value = []
    ElMessage.info('正在生成图像，请稍候...')

    const result = await generateImageToImage(prompt.value, uploadedImageFile.value, {
      aspectRatio: selectedSize.value,
      n: generateCount.value
    })

    if (result.success) {
      generatedImages.value = result.images
      
      // 创建绘画记录
      const drawing = {
        id: Date.now().toString(),
        prompt: prompt.value,
        imageUrl: result.images[0].url,
        images: result.images,
        type: 'image-to-image',
        model: result.model,
        size: result.size,
        createdAt: new Date().toISOString(),
        isFavorite: false
      }

      // 添加到历史记录
      drawingStore.addDrawing(drawing)
      drawingStore.setCurrentDrawing(drawing)

      ElMessage.success(`成功生成 ${result.images.length} 张图像`)
      
      // 减少配额
      remainingQuota.value = Math.max(0, remainingQuota.value - 1)
    } else {
      ElMessage.error(result.error || '图像生成失败')
    }
  } catch (error) {
    console.error('图文生图失败:', error)
    ElMessage.error('图像生成失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

const downloadImage = async (url, index) => {
  const { downloadImage: downloadUtil } = await import('@/utils/download.js')
  const filename = `image-to-image-${Date.now()}-${index + 1}.png`
  await downloadUtil(url, filename)
}

const saveToGallery = (image, index) => {
  // 这里可以实现保存到画廊的功能
  ElMessage.success('已保存到画廊')
}
</script>
