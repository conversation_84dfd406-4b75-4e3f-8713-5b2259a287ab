import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { APP_CONFIG, DEFAULT_SETTINGS } from '@/config'
// import { loginApi, logoutApi, getUserInfoApi, updateUserInfoApi } from '@/api/user'
import { getToken, setToken, removeToken, getTokenSync } from '@/utils/auth'
import loginStateManager, { initializeLoginStateOnPageLoad } from '@/utils/loginStateManager'

export const useUserStore = defineStore('user', () => {
  // 状态 - 延迟初始化token，避免在模块加载时调用getToken()
  const token = ref('')
  const userInfo = ref(null)
  const settings = ref({ ...DEFAULT_SETTINGS })
  const loading = ref(false)

  // 状态初始化标志
  const isInitialized = ref(false)

  // 初始化token（异步方法）
  const initToken = async () => {
    if (!token.value) {
      try {
        const savedToken = await getToken()
        if (savedToken && !isTokenExpired(savedToken)) {
          token.value = savedToken
          console.log('✅ 初始化token成功:', savedToken.substring(0, 20) + '...')
        } else if (savedToken && isTokenExpired(savedToken)) {
          console.log('⚠️ Token已过期，清除过期token')
          await removeToken()
          token.value = ''
        } else {
          token.value = ''
        }
      } catch (error) {
        console.error('❌ 初始化token失败:', error)
        // 降级到同步方法
        const fallbackToken = getTokenSync()
        if (fallbackToken && !isTokenExpired(fallbackToken)) {
          token.value = fallbackToken
          console.log('⚠️ 使用降级方法初始化token成功')
        } else {
          token.value = ''
        }
      }
    }
  }

  // 初始化用户信息（同步方法）
  const initUserInfo = () => {
    if (!userInfo.value) {
      try {
        const savedUserInfo = localStorage.getItem(APP_CONFIG.storage.userKey)
        if (savedUserInfo) {
          const parsedUserInfo = JSON.parse(savedUserInfo)
          // 验证用户信息的完整性
          if (parsedUserInfo && parsedUserInfo.username && parsedUserInfo.id) {
            userInfo.value = parsedUserInfo
            console.log('✅ 初始化用户信息成功:', parsedUserInfo.username)
          } else {
            console.warn('⚠️ 用户信息不完整，清除无效数据')
            localStorage.removeItem(APP_CONFIG.storage.userKey)
            userInfo.value = null
          }
        }
      } catch (error) {
        console.warn('⚠️ 初始化用户信息失败:', error)
        localStorage.removeItem(APP_CONFIG.storage.userKey)
        userInfo.value = null
      }
    }
  }

  // 计算属性 - 简化逻辑，避免副作用
  const isLoggedIn = computed(() => {
    const hasValidToken = !!token.value && !isTokenExpired(token.value)
    const hasValidUserInfo = !!userInfo.value && !!userInfo.value.username && !!userInfo.value.id
    const loginStatus = hasValidToken && hasValidUserInfo

    // 只在开发环境输出详细日志
    if (import.meta.env.DEV) {
      console.log('isLoggedIn 计算:', {
        hasValidToken,
        hasValidUserInfo,
        loginStatus,
        username: userInfo.value?.username,
        isInitialized: isInitialized.value
      })
    }

    return loginStatus
  })
  const userName = computed(() => userInfo.value?.username || '游客')
  const userAvatar = computed(() => userInfo.value?.avatar || '/default-avatar.svg')
  const userRole = computed(() => userInfo.value?.role || 'user')
  const isAdmin = computed(() => userRole.value === 'admin')
  const isVip = computed(() => userRole.value === 'vip')

  // 获取本地存储的用户数据库
  const getLocalUserDatabase = () => {
    try {
      const usersData = localStorage.getItem('ai_creative_users_db')
      return usersData ? JSON.parse(usersData) : []
    } catch (error) {
      console.error('获取本地用户数据库失败:', error)
      return []
    }
  }

  // 保存用户到本地数据库
  const saveUserToDatabase = (userData) => {
    try {
      const users = getLocalUserDatabase()
      users.push(userData)
      localStorage.setItem('ai_creative_users_db', JSON.stringify(users))
      return true
    } catch (error) {
      console.error('保存用户到本地数据库失败:', error)
      return false
    }
  }

  // 检查用户名或邮箱是否已存在
  const checkUserExists = (username, email) => {
    const users = getLocalUserDatabase()
    return users.some(user =>
      user.username === username || user.email === email
    )
  }

  // 注册 - 前端模拟
  const register = async (registerForm) => {
    console.log('用户store: 开始注册', registerForm)

    try {
      loading.value = true

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1500))

      // 检查用户名或邮箱是否已存在
      if (checkUserExists(registerForm.username, registerForm.email)) {
        ElMessage({
          message: '用户名或邮箱已存在，请使用其他用户名或邮箱',
          type: 'error',
          duration: 4000,
          showClose: true,
          customClass: 'high-contrast-message',
          center: true
        })
        return { success: false, message: '用户名或邮箱已存在' }
      }

      // 创建新用户数据
      const newUser = {
        id: Date.now(),
        username: registerForm.username,
        email: registerForm.email,
        password: registerForm.password, // 实际项目中应该加密
        avatar: '/default-avatar.svg',
        role: 'user',
        nickname: registerForm.username,
        createdAt: new Date().toISOString(),
        lastLoginAt: null,
        isActive: true,
        profile: {
          bio: '',
          location: '',
          website: '',
          birthday: null,
          gender: null
        },
        preferences: {
          theme: 'light',
          language: 'zh-CN',
          notifications: {
            email: true,
            push: true,
            marketing: false
          }
        }
      }

      // 保存用户到本地数据库
      if (saveUserToDatabase(newUser)) {
        console.log('用户store: 注册成功', newUser)
        ElMessage({
          message: '注册成功！请登录您的账户',
          type: 'success',
          duration: 4000,
          showClose: true,
          customClass: 'high-contrast-message',
          center: true
        })
        return { success: true, user: newUser }
      } else {
        throw new Error('保存用户数据失败')
      }
    } catch (error) {
      console.error('用户store: 注册错误:', error)
      ElMessage({
        message: '注册失败，请稍后重试',
        type: 'error',
        duration: 4000,
        showClose: true,
        customClass: 'high-contrast-message',
        center: true
      })
      return { success: false, message: '注册失败' }
    } finally {
      loading.value = false
    }
  }

  // 登录 - 前端模拟
  const login = async (loginForm) => {
    console.log('用户store: 开始登录', loginForm)

    try {
      loading.value = true

      // 模拟API调用延迟
      console.log('用户store: 模拟API延迟')
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 获取本地用户数据库
      const localUsers = getLocalUserDatabase()

      // 默认管理员账户
      const defaultUsers = [
        { username: 'admin', password: '123456', role: 'admin', email: '<EMAIL>' },
        { username: 'user', password: '123456', role: 'user', email: '<EMAIL>' },
        { username: 'test', password: '123456', role: 'user', email: '<EMAIL>' }
      ]

      // 合并默认用户和注册用户
      const allUsers = [...defaultUsers, ...localUsers]

      console.log('用户store: 查找用户', { username: loginForm.username, password: loginForm.password })

      const user = allUsers.find(u =>
        (u.username === loginForm.username || u.email === loginForm.username) &&
        u.password === loginForm.password
      )

      console.log('用户store: 找到的用户', user)

      if (user) {
        // 生成模拟token，包含过期时间（30天）
        const expirationTime = Date.now() + (30 * 24 * 60 * 60 * 1000) // 30天后过期
        const mockToken = 'mock_token_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11) + '_exp_' + expirationTime

        // 生成模拟用户信息
        const mockUserInfo = {
          id: user.id || Date.now(),
          username: user.username,
          email: user.email,
          avatar: user.avatar || '/default-avatar.svg',
          role: user.role || 'user',
          nickname: user.nickname || user.username,
          createdAt: user.createdAt || new Date().toISOString(),
          lastLoginAt: new Date().toISOString(),
          profile: user.profile || {
            bio: '',
            location: '',
            website: '',
            birthday: null,
            gender: null
          },
          preferences: user.preferences || {
            theme: 'light',
            language: 'zh-CN',
            notifications: {
              email: true,
              push: true,
              marketing: false
            }
          }
        }

        console.log('用户store: 设置token和用户信息', { token: mockToken, userInfo: mockUserInfo })

        token.value = mockToken
        userInfo.value = mockUserInfo

        // 使用登录状态管理器保存登录状态
        const saveSuccess = loginStateManager.save(mockToken, mockUserInfo, {
          rememberMe: loginForm.rememberMe,
          expiresAt: expirationTime
        })

        if (!saveSuccess) {
          console.warn('⚠️ 保存登录状态失败，使用备用方法')
          // 备用保存方法
          await setToken(token.value, { expiresAt: expirationTime })
          localStorage.setItem(APP_CONFIG.storage.userKey, JSON.stringify(userInfo.value))
        }

        // 更新用户最后登录时间（如果是注册用户）
        if (user.id && localUsers.some(u => u.id === user.id)) {
          const updatedUsers = localUsers.map(u =>
            u.id === user.id ? { ...u, lastLoginAt: new Date().toISOString() } : u
          )
          localStorage.setItem('ai_creative_users_db', JSON.stringify(updatedUsers))
        }

        // 如果选择了记住我，保存登录信息（90天）
        if (loginForm.rememberMe) {
          localStorage.setItem('ai_creative_remember_me', JSON.stringify({
            username: loginForm.username,
            timestamp: Date.now()
          }))
        } else {
          localStorage.removeItem('ai_creative_remember_me')
        }

        // 保存登录时间戳，用于自动登录验证
        localStorage.setItem('ai_creative_login_timestamp', Date.now().toString())

        // 标记初始化完成
        isInitialized.value = true

        console.log('用户store: 登录成功')
        return { success: true }
      } else {
        console.log('用户store: 用户名或密码错误')
        ElMessage({
          message: '用户名或密码错误，请检查后重试',
          type: 'error',
          duration: 4000,
          showClose: true,
          customClass: 'high-contrast-message',
          center: true
        })
        return { success: false, message: '用户名或密码错误' }
      }
    } catch (error) {
      console.error('用户store: 登录错误:', error)
      ElMessage({
        message: '登录失败，请稍后重试',
        type: 'error',
        duration: 4000,
        showClose: true,
        customClass: 'high-contrast-message',
        center: true
      })
      return { success: false, message: '登录失败' }
    } finally {
      loading.value = false
    }
  }

  // 获取记住我信息
  const getRememberMeInfo = () => {
    try {
      const rememberData = localStorage.getItem('ai_creative_remember_me')
      if (rememberData) {
        const data = JSON.parse(rememberData)
        // 检查是否过期（90天）
        const ninetyDays = 90 * 24 * 60 * 60 * 1000
        if (Date.now() - data.timestamp < ninetyDays) {
          return data.username
        } else {
          localStorage.removeItem('ai_creative_remember_me')
        }
      }
      return null
    } catch (error) {
      console.error('获取记住我信息失败:', error)
      return null
    }
  }

  // 检查token是否过期
  const isTokenExpired = (token) => {
    if (!token) return true
    
    try {
      // 从token中提取过期时间
      const parts = token.split('_exp_')
      if (parts.length === 2) {
        const expirationTime = parseInt(parts[1])
        return Date.now() > expirationTime
      }
      
      // 如果没有过期时间信息，检查登录时间戳（默认30天）
      const loginTimestamp = localStorage.getItem('ai_creative_login_timestamp')
      if (loginTimestamp) {
        const thirtyDays = 30 * 24 * 60 * 60 * 1000
        return Date.now() - parseInt(loginTimestamp) > thirtyDays
      }
      
      return true
    } catch (error) {
      console.error('检查token过期失败:', error)
      return true
    }
  }

  // 更新用户信息
  const updateUserInfo = async (updates) => {
    try {
      loading.value = true

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 更新当前用户信息
      userInfo.value = { ...userInfo.value, ...updates }

      // 保存到本地存储
      localStorage.setItem(APP_CONFIG.storage.userKey, JSON.stringify(userInfo.value))

      // 如果是注册用户，更新本地数据库
      const localUsers = getLocalUserDatabase()
      const userIndex = localUsers.findIndex(u => u.id === userInfo.value.id)
      if (userIndex !== -1) {
        localUsers[userIndex] = { ...localUsers[userIndex], ...updates }
        localStorage.setItem('ai_creative_users_db', JSON.stringify(localUsers))
      }

      ElMessage({
        message: '用户信息更新成功',
        type: 'success',
        duration: 3000,
        showClose: true,
        customClass: 'high-contrast-message',
        center: true
      })

      return { success: true }
    } catch (error) {
      console.error('更新用户信息失败:', error)
      ElMessage({
        message: '更新失败，请稍后重试',
        type: 'error',
        duration: 4000,
        showClose: true,
        customClass: 'high-contrast-message',
        center: true
      })
      return { success: false, message: '更新失败' }
    } finally {
      loading.value = false
    }
  }

  // 登出 - 前端模拟
  const logout = async () => {
    try {
      loading.value = true

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 500))

      // 清除状态
      token.value = ''
      userInfo.value = null
      isInitialized.value = false

      // 使用登录状态管理器清除登录状态
      const clearSuccess = loginStateManager.clear()

      if (!clearSuccess) {
        console.warn('⚠️ 清除登录状态失败，使用备用方法')
        // 备用清除方法
        await removeToken()
        localStorage.removeItem(APP_CONFIG.storage.userKey)
        localStorage.removeItem(APP_CONFIG.storage.settingsKey)
        localStorage.removeItem('ai_creative_login_timestamp')
      }
      // 不清除记住我信息，除非用户主动取消

      ElMessage.success('已退出登录')
    } catch (error) {
      console.error('登出错误:', error)
      // 即使出错，也要清除本地状态
      token.value = ''
      userInfo.value = null
      isInitialized.value = false
      await removeToken()
      localStorage.removeItem(APP_CONFIG.storage.userKey)
      localStorage.removeItem('ai_creative_login_timestamp')
    } finally {
      loading.value = false
    }
  }

  // 获取用户信息 - 前端模拟
  const getUserInfo = async () => {
    try {
      if (!token.value) return false

      loading.value = true

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 500))

      // 如果已有用户信息，直接返回
      if (userInfo.value) {
        return true
      }

      // 模拟从token获取用户信息失败（token过期）
      if (Math.random() > 0.9) {
        await logout()
        return false
      }

      return true
    } catch (error) {
      console.error('获取用户信息错误:', error)
      await logout()
      return false
    } finally {
      loading.value = false
    }
  }



  // 更新用户设置
  const updateSettings = (newSettings) => {
    settings.value = { ...settings.value, ...newSettings }
    localStorage.setItem(APP_CONFIG.storage.settingsKey, JSON.stringify(settings.value))
  }

  // 初始化用户状态
  const initUserState = async () => {
    try {
      console.log('👤 开始初始化用户状态...')

      // 防止重复初始化
      if (isInitialized.value) {
        console.log('ℹ️ 用户状态已初始化，跳过重复初始化')
        return
      }

      // 使用页面加载专用的初始化函数
      const restoredState = initializeLoginStateOnPageLoad()

      if (restoredState) {
        // 恢复成功
        token.value = restoredState.token
        userInfo.value = restoredState.userInfo
        console.log('✅ 页面加载登录状态恢复成功')
      } else {
        // 没有有效的登录状态
        console.log('ℹ️ 页面加载时没有有效的登录状态')
        token.value = ''
        userInfo.value = null
      }

      // 从本地存储恢复用户设置（安全处理）
      try {
        const savedSettings = localStorage.getItem(APP_CONFIG.storage.settingsKey)
        if (savedSettings) {
          settings.value = { ...DEFAULT_SETTINGS, ...JSON.parse(savedSettings) }
          console.log('✅ 恢复用户设置成功')
        } else {
          settings.value = { ...DEFAULT_SETTINGS }
          console.log('ℹ️ 使用默认用户设置')
        }
      } catch (settingsError) {
        console.warn('⚠️ 恢复用户设置失败:', settingsError)
        // 使用默认设置
        localStorage.removeItem(APP_CONFIG.storage.settingsKey)
        settings.value = { ...DEFAULT_SETTINGS }
      }

      // 验证登录状态的一致性
      const hasValidToken = !!token.value && !isTokenExpired(token.value)
      const hasValidUserInfo = !!userInfo.value && !!userInfo.value.username && !!userInfo.value.id
      const currentLoginStatus = hasValidToken && hasValidUserInfo

      console.log('👤 用户状态检查:', {
        hasToken: !!token.value,
        tokenValid: hasValidToken,
        hasUserInfo: hasValidUserInfo,
        isLoggedIn: currentLoginStatus,
        username: userInfo.value?.username
      })

      // 如果token和用户信息不匹配，清除状态
      if (token.value && !hasValidToken) {
        console.warn('⚠️ Token已过期，清除登录状态')
        await logout()
      } else if (hasValidToken && !hasValidUserInfo) {
        console.warn('⚠️ 有token但没有有效用户信息，清除token')
        removeToken()
        token.value = ''
      } else if (!hasValidToken && hasValidUserInfo) {
        console.warn('⚠️ 有用户信息但没有有效token，清除用户信息')
        localStorage.removeItem(APP_CONFIG.storage.userKey)
        userInfo.value = null
      }

      // 标记初始化完成
      isInitialized.value = true

      console.log('✅ 用户状态初始化完成，最终登录状态:', isLoggedIn.value)
    } catch (error) {
      console.error('❌ 用户状态初始化失败:', error)
      // 重置状态以确保应用能正常运行
      try {
        resetState()
        console.log('✅ 用户状态已重置为默认值')
      } catch (resetError) {
        console.error('❌ 重置用户状态也失败了:', resetError)
      }
      // 不再抛出错误，让应用继续运行
      console.warn('⚠️ 用户状态初始化失败，将以游客模式运行')
    }
  }

  // 重置状态
  const resetState = () => {
    token.value = ''
    userInfo.value = null
    settings.value = { ...DEFAULT_SETTINGS }
    loading.value = false
    isInitialized.value = false
  }

  return {
    // 状态
    token,
    userInfo,
    settings,
    loading,
    isInitialized,

    // 计算属性
    isLoggedIn,
    userName,
    userAvatar,
    userRole,
    isAdmin,
    isVip,

    // 方法
    register,
    login,
    logout,
    getUserInfo,
    updateUserInfo,
    updateSettings,
    getRememberMeInfo,
    getLocalUserDatabase,
    checkUserExists,
    initUserState,
    resetState,
    isTokenExpired,
  }
})
