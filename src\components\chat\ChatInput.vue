<template>
  <div class="input-area">
    <!-- 文件上传预览 -->
    <FilePreview
      v-if="uploadedFiles.length > 0"
      :files="uploadedFiles"
      @remove-file="removeFile"
      @clear-files="uploadedFiles = []"
    />

    <div class="input-container">
      <div class="input-wrapper">
        <div class="input-tools">
          <button @click="triggerFileUpload" class="tool-btn" title="上传文件">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4M17 8l-5-5-5 5M12 3v12" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>

          <button class="tool-btn" title="图片上传">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
              <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
              <path d="M21 15l-5-5L5 21" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>

          <button
            @click="startVoiceInput"
            :class="['tool-btn', 'voice-tool', { recording: isRecording }]"
            title="语音输入"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z" stroke="currentColor" stroke-width="2"/>
              <path d="M19 10v2a7 7 0 0 1-14 0v-2" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>
        </div>

        <div class="input-field">
          <textarea
            ref="messageInput"
            v-model="inputMessage"
            placeholder="输入您的问题，我来帮您解答... (支持Ctrl+V粘贴图片)"
            @keydown.enter.exact.prevent="handleSendMessage"
            @keydown.enter.shift.exact="inputMessage += '\n'"
            @input="handleInputChange"
            @keydown="handleKeyDown"
            @paste="handlePaste"
            @focus="inputFocused = true"
            @blur="inputFocused = false"
            :disabled="loading"
            class="message-input"
            rows="1"
          ></textarea>

          <button
            @click="handleSendMessage"
            :disabled="!canSend"
            class="send-btn"
            :class="{ active: canSend }"
          >
            <svg v-if="loading" width="20" height="20" viewBox="0 0 24 24" fill="none" class="loading-icon">
              <path d="M21 12a9 9 0 1 1-6.219-8.56" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      multiple
      accept=".txt,.pdf,.doc,.docx,.md,.jpg,.jpeg,.png,.gif"
      @change="handleFileUpload"
      style="display: none"
    />
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import { debounce } from 'lodash-es'
import QuickCommands from './QuickCommands.vue'
import FilePreview from './FilePreview.vue'

const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

defineEmits([
  'send-message',
  'start-voice-input',
  'file-upload'
])

// 响应式数据
const inputMessage = ref('')
const showQuickCommands = ref(false)
const uploadedFiles = ref([])
const isRecording = ref(false)
const inputFocused = ref(false)
const messageInput = ref(null)
const fileInput = ref(null)

// 快捷指令数据
const quickCommands = ref([
  { id: 1, icon: '✍️', label: '写作助手', text: '帮我写一篇关于', desc: '创作文章、报告等' },
  { id: 2, icon: '🔍', label: '解释说明', text: '请解释一下', desc: '解释概念、原理等' },
  { id: 3, icon: '💡', label: '创意建议', text: '给我一些关于...的创意建议', desc: '获取创意和建议' },
  { id: 4, icon: '📊', label: '数据分析', text: '帮我分析这些数据', desc: '分析和总结数据' },
  { id: 5, icon: '🛠️', label: '问题解决', text: '如何解决...问题', desc: '解决具体问题' },
  { id: 6, icon: '📚', label: '学习辅导', text: '教我学习', desc: '学习新知识技能' }
])

// 计算属性
const canSend = computed(() => {
  return inputMessage.value.trim().length > 0 && !props.loading && !props.disabled
})

// 方法
const handleInputChange = debounce(() => {
  // 自动调整输入框高度
  nextTick(() => {
    if (messageInput.value) {
      messageInput.value.style.height = 'auto'
      messageInput.value.style.height = Math.min(messageInput.value.scrollHeight, 120) + 'px'
    }
  })
}, 100)

const handleSendMessage = () => {
  if (!canSend.value) return
  
  const message = inputMessage.value.trim()
  if (message) {
    emit('send-message', message)
    inputMessage.value = ''
    
    // 重置输入框高度
    nextTick(() => {
      if (messageInput.value) {
        messageInput.value.style.height = 'auto'
      }
    })
  }
}

const handleKeyDown = (event) => {
  // 处理快捷指令触发
  if (event.key === '/' && inputMessage.value === '') {
    event.preventDefault()
    showQuickCommands.value = true
  }
}

const toggleQuickCommands = () => {
  showQuickCommands.value = !showQuickCommands.value
}

const insertCommand = (command) => {
  inputMessage.value = command
  showQuickCommands.value = false
  
  // 聚焦到输入框
  nextTick(() => {
    if (messageInput.value) {
      messageInput.value.focus()
      messageInput.value.setSelectionRange(command.length, command.length)
    }
  })
}

const triggerFileUpload = () => {
  fileInput.value?.click()
}

const handleFileUpload = (event) => {
  const files = Array.from(event.target.files)
  
  files.forEach(file => {
    const fileObj = {
      id: Date.now() + Math.random(),
      name: file.name,
      size: file.size,
      type: file.type,
      file: file
    }
    uploadedFiles.value.push(fileObj)
  })
  
  emit('file-upload', uploadedFiles.value)
  
  // 清空文件输入
  event.target.value = ''
}

const removeFile = (fileId) => {
  const index = uploadedFiles.value.findIndex(f => f.id === fileId)
  if (index > -1) {
    uploadedFiles.value.splice(index, 1)
  }
}

const startVoiceInput = () => {
  isRecording.value = !isRecording.value
  emit('start-voice-input', isRecording.value)
}

// 处理粘贴事件
const handlePaste = (event) => {
  const clipboardData = event.clipboardData || window.clipboardData
  const items = clipboardData.items

  // 检查是否有图片文件
  for (let i = 0; i < items.length; i++) {
    const item = items[i]

    // 如果是图片类型
    if (item.type.indexOf('image') !== -1) {
      event.preventDefault() // 阻止默认粘贴行为

      const file = item.getAsFile()
      if (file) {
        // 创建文件对象
        const fileObj = {
          id: Date.now() + Math.random(),
          name: `粘贴的图片_${new Date().toLocaleString()}.${file.type.split('/')[1]}`,
          size: file.size,
          type: file.type,
          file: file
        }

        // 添加到上传文件列表
        uploadedFiles.value.push(fileObj)
        emit('file-upload', uploadedFiles.value)

        // 显示成功提示
        console.log('图片粘贴成功:', fileObj.name)
      }
      break
    }
  }
}

// 暴露方法给父组件
defineExpose({
  focus: () => messageInput.value?.focus(),
  clear: () => {
    inputMessage.value = ''
    uploadedFiles.value = []
  }
})
</script>

<style lang="scss" scoped>
.input-area {
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  padding: 1rem 0;

  .input-container {
    padding: 0 1.5rem;
    max-width: 800px;
    margin: 0 auto;

    .input-wrapper {
      display: flex;
      align-items: flex-end;
      gap: 0.75rem;
      background: white;
      border: 2px solid #e2e8f0;
      border-radius: 24px;
      padding: 0.75rem 1rem;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

      &:focus-within {
        border-color: #667eea;
        box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1), 0 8px 24px rgba(0, 0, 0, 0.12);
        transform: translateY(-1px);
      }

      .input-tools {
        display: flex;
        gap: 0.25rem;
        padding-left: 0.5rem;

        .tool-btn {
          width: 36px;
          height: 36px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f8fafc;
          border: 1px solid #e2e8f0;
          border-radius: 10px;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          color: #64748b;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.2) 0%, transparent 70%);
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
          }

          &:hover {
            background: #f1f5f9;
            color: #475569;
            border-color: #cbd5e1;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

            &::before {
              width: 100%;
              height: 100%;
            }
          }

          &.voice-tool.recording {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            border-color: #dc2626;
            animation: pulse 2s infinite;
          }
        }
      }

      .input-field {
        flex: 1;
        display: flex;
        align-items: flex-end;
        gap: 0.5rem;

        .message-input {
          flex: 1;
          background: transparent;
          border: none;
          outline: none;
          color: #1f2937;
          font-size: 1rem;
          line-height: 1.5;
          resize: none;
          min-height: 24px;
          max-height: 120px;
          padding: 0.5rem 0.75rem;
          transition: all 0.2s ease;

          &::placeholder {
            color: #94a3b8;
            transition: color 0.2s ease;
          }

          &:focus::placeholder {
            color: #cbd5e1;
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }

        .send-btn {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f1f5f9;
          border: none;
          border-radius: 20px;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          color: #64748b;

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          &.active:not(:disabled) {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            transform: scale(1.05);

            &:hover {
              box-shadow: 0 6px 16px rgba(102, 126, 234, 0.5);
              transform: scale(1.08);
            }
          }

          &:hover:not(:disabled):not(.active) {
            background: #e2e8f0;
            color: #475569;
            transform: scale(1.05);
          }

          .loading-icon {
            animation: spin 1s linear infinite;
          }
        }
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.6);
  }
}

// 移动端响应式优化
@media (max-width: 768px) {
  .chat-input {
    padding: 0.75rem; // 减少外边距

    .input-container {
      padding: 0.75rem; // 减少容器内边距

      .input-tools {
        gap: 0.375rem; // 减少工具按钮间距

        .tool-btn {
          width: 36px; // 确保触摸目标足够大
          height: 36px;
          border-radius: 8px; // 稍小圆角

          &:hover {
            transform: none; // 移动端移除悬停效果
          }
        }
      }

      .input-field {
        gap: 0.375rem; // 减少输入区域间距

        .message-input {
          font-size: 16px; // 防止iOS缩放
          padding: 0.625rem 0.75rem; // 调整内边距
          min-height: 20px; // 稍小最小高度
          max-height: 100px; // 减少最大高度
          line-height: 1.4; // 调整行高
        }

        .send-btn {
          width: 44px; // 确保触摸目标足够大
          height: 44px;
          border-radius: 22px;

          &:hover:not(:disabled):not(.active) {
            transform: none; // 移动端移除悬停效果
            background: #e2e8f0;
          }

          &.active:not(:disabled) {
            transform: none; // 移动端移除缩放效果

            &:hover {
              transform: none;
              box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            }
          }
        }
      }
    }
  }
}

// 超小屏幕优化 (480px以下)
@media (max-width: 480px) {
  .chat-input {
    padding: 0.5rem; // 更紧凑的外边距

    .input-container {
      padding: 0.625rem; // 更紧凑的容器内边距

      .input-tools {
        gap: 0.25rem; // 进一步减少工具按钮间距

        .tool-btn {
          width: 32px; // 超小屏幕上稍小按钮
          height: 32px;
        }
      }

      .input-field {
        gap: 0.25rem; // 进一步减少间距

        .message-input {
          padding: 0.5rem 0.625rem; // 更紧凑的内边距
          max-height: 80px; // 进一步减少最大高度
        }

        .send-btn {
          width: 40px; // 超小屏幕上稍小发送按钮
          height: 40px;
          border-radius: 20px;
        }
      }
    }
  }
}

// iOS Safari 安全区域适配
@supports (-webkit-touch-callout: none) {
  @media (max-width: 768px) {
    .chat-input {
      padding-bottom: calc(0.75rem + env(safe-area-inset-bottom));
    }
  }

  @media (max-width: 480px) {
    .chat-input {
      padding-bottom: calc(0.5rem + env(safe-area-inset-bottom));
    }
  }
}
</style>
