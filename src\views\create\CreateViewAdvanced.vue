<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-pink-50">
    <!-- 顶部导航栏 -->
    <header class="bg-white/80 backdrop-blur-xl border-b border-gray-200/50 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <h1 class="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              AI 创作工坊
            </h1>
            <div class="flex items-center space-x-2 text-sm text-gray-600">
              <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>AI引擎运行中</span>
            </div>
          </div>
          
          <div class="flex items-center space-x-4">
            <!-- 历史记录 -->
            <button
              @click="showHistory = true"
              class="flex items-center space-x-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2"/>
              </svg>
              <span>历史记录</span>
            </button>
            
            <!-- 我的作品 -->
            <button
              @click="showMyWorks = true"
              class="flex items-center space-x-2 px-4 py-2 bg-purple-100 hover:bg-purple-200 text-purple-700 rounded-lg transition-colors"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17l-3-3 1.5-1.5L9 14l7-7L17.5 8.5 9 17z" fill="currentColor"/>
              </svg>
              <span>我的作品</span>
            </button>

            <!-- 图文生图 -->
            <router-link
              to="/image-to-image"
              class="flex items-center space-x-2 px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg transition-colors"
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
                <polyline points="21,15 16,10 5,21" stroke="currentColor" stroke-width="2"/>
              </svg>
              <span>图文生图</span>
            </router-link>
          </div>
        </div>
      </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- 左侧控制面板 -->
        <div class="lg:col-span-1 space-y-6">
          <!-- 快速开始 -->
          <div class="bg-white rounded-2xl shadow-lg p-6">
            <h3 class="text-lg font-semibold mb-4 flex items-center">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" class="mr-2 text-purple-600">
                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" stroke="currentColor" stroke-width="2"/>
              </svg>
              快速开始
            </h3>
            
            <div class="grid grid-cols-2 gap-3 mb-4">
              <button
                v-for="template in quickTemplates"
                :key="template.id"
                @click="applyTemplate(template)"
                class="p-3 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-400 hover:bg-purple-50 transition-all text-sm"
              >
                <div class="text-2xl mb-1">{{ template.icon }}</div>
                <div class="font-medium">{{ template.name }}</div>
              </button>
            </div>
          </div>

          <!-- 提示词输入 -->
          <div class="bg-white rounded-2xl shadow-lg p-6">
            <h3 class="text-lg font-semibold mb-4">创作描述</h3>
            
            <!-- 智能提示词助手 -->
            <div class="mb-4">
              <button
                @click="showPromptHelper = !showPromptHelper"
                class="flex items-center space-x-2 text-sm text-purple-600 hover:text-purple-700"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>AI提示词助手</span>
              </button>
            </div>

            <textarea
              v-model="prompt"
              placeholder="描述你想要创作的画面，比如：一只可爱的小猫坐在彩虹桥上，背景是星空..."
              rows="4"
              class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none"
            ></textarea>

            <!-- 提示词建议 -->
            <div v-if="showPromptHelper" class="mt-4 p-4 bg-purple-50 rounded-lg">
              <h4 class="font-medium mb-2">提示词建议：</h4>
              <div class="flex flex-wrap gap-2">
                <button
                  v-for="suggestion in promptSuggestions"
                  :key="suggestion"
                  @click="addToPrompt(suggestion)"
                  class="px-3 py-1 bg-white border border-purple-200 rounded-full text-sm hover:bg-purple-100 transition-colors"
                >
                  {{ suggestion }}
                </button>
              </div>
            </div>

            <!-- 负面提示词 -->
            <div class="mt-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">负面提示词（不想要的元素）</label>
              <textarea
                v-model="negativePrompt"
                placeholder="描述不想要的元素，比如：模糊、低质量、变形..."
                rows="2"
                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none"
              ></textarea>
            </div>
          </div>

          <!-- 高级参数设置 -->
          <div class="bg-white rounded-2xl shadow-lg p-6">
            <h3 class="text-lg font-semibold mb-4">高级设置</h3>
            
            <!-- AI模型选择 -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">AI模型</label>
              <select
                v-model="selectedModel"
                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"
              >
                <option v-for="model in aiModels" :key="model.id" :value="model.id">
                  {{ model.name }} - {{ model.description }}
                </option>
              </select>
            </div>

            <!-- 图片尺寸 -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">图片尺寸</label>
              <div class="grid grid-cols-2 gap-2">
                <button
                  v-for="size in imageSizes"
                  :key="size.value"
                  @click="selectedSize = size.value"
                  class="p-3 border rounded-lg text-sm transition-colors"
                  :class="selectedSize === size.value 
                    ? 'border-purple-500 bg-purple-50 text-purple-700' 
                    : 'border-gray-300 hover:border-gray-400'"
                >
                  <div class="font-medium">{{ size.label }}</div>
                  <div class="text-xs text-gray-500">{{ size.value }}</div>
                </button>
              </div>
            </div>

            <!-- 艺术风格 -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">艺术风格</label>
              <div class="grid grid-cols-2 gap-2">
                <button
                  v-for="style in artStyles"
                  :key="style.id"
                  @click="selectedStyle = style.id"
                  class="p-3 border rounded-lg text-sm transition-colors"
                  :class="selectedStyle === style.id 
                    ? 'border-purple-500 bg-purple-50 text-purple-700' 
                    : 'border-gray-300 hover:border-gray-400'"
                >
                  {{ style.name }}
                </button>
              </div>
            </div>

            <!-- 质量设置 -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                图片质量: {{ quality }}
              </label>
              <input
                v-model="quality"
                type="range"
                min="1"
                max="10"
                class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
              />
              <div class="flex justify-between text-xs text-gray-500 mt-1">
                <span>快速</span>
                <span>高质量</span>
              </div>
            </div>

            <!-- 创意程度 -->
            <div class="mb-6">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                创意程度: {{ creativity }}
              </label>
              <input
                v-model="creativity"
                type="range"
                min="1"
                max="10"
                class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
              />
              <div class="flex justify-between text-xs text-gray-500 mt-1">
                <span>保守</span>
                <span>创新</span>
              </div>
            </div>

            <!-- 生成按钮 -->
            <button
              @click="generateImage"
              :disabled="!prompt.trim() || isGenerating"
              class="w-full py-4 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all transform hover:scale-105"
            >
              <div v-if="isGenerating" class="flex items-center justify-center space-x-2">
                <div class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>AI创作中... ({{ generationProgress }}%)</span>
              </div>
              <div v-else class="flex items-center justify-center space-x-2">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" fill="currentColor"/>
                </svg>
                <span>开始创作</span>
              </div>
            </button>
          </div>
        </div>

        <!-- 右侧预览区域 -->
        <div class="lg:col-span-2">
          <!-- 生成结果展示 -->
          <div class="bg-white rounded-2xl shadow-lg p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold">创作结果</h3>
              <div class="flex items-center space-x-2">
                <button
                  v-if="generatedImages.length > 0"
                  @click="downloadAll"
                  class="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4m4-5l5 5 5-5m-5 5V3" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  <span>全部下载</span>
                </button>
              </div>
            </div>

            <!-- 图片展示区 -->
            <div class="min-h-96 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
              <div v-if="isGenerating" class="text-center">
                <div class="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <h4 class="text-lg font-semibold text-gray-900 mb-2">AI正在创作中...</h4>
                <p class="text-gray-600 mb-4">{{ generationStatus }}</p>
                <div class="w-64 bg-gray-200 rounded-full h-2 mx-auto">
                  <div 
                    class="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-300"
                    :style="{ width: generationProgress + '%' }"
                  ></div>
                </div>
              </div>

              <div v-else-if="generatedImages.length === 0" class="text-center">
                <svg width="64" height="64" viewBox="0 0 24 24" fill="none" class="mx-auto mb-4 text-gray-400">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" stroke-width="2"/>
                </svg>
                <h4 class="text-lg font-semibold text-gray-900 mb-2">准备开始创作</h4>
                <p class="text-gray-600">输入你的创意描述，让AI为你创作独特的艺术作品</p>
              </div>

              <div v-else class="w-full">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div
                    v-for="(image, index) in generatedImages"
                    :key="index"
                    class="relative group cursor-pointer"
                    @click="openImageViewer(image, index)"
                  >
                    <img
                      :src="image.url"
                      :alt="`生成的图片 ${index + 1}`"
                      class="w-full h-64 object-cover rounded-lg shadow-md group-hover:shadow-lg transition-shadow"
                    />
                    
                    <!-- 悬浮操作层 -->
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                      <div class="flex space-x-2">
                        <button
                          @click.stop="downloadImage(image)"
                          class="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                        >
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4m4-5l5 5 5-5m-5 5V3" stroke="currentColor" stroke-width="2"/>
                          </svg>
                        </button>
                        <button
                          @click.stop="shareImage(image)"
                          class="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                        >
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M4 12v8a2 2 0 002 2h12a2 2 0 002-2v-8M16 6l-4-4-4 4M12 2v13" stroke="currentColor" stroke-width="2"/>
                          </svg>
                        </button>
                        <button
                          @click.stop="saveToGallery(image)"
                          class="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                        >
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M19 21l-7-5-7 5V5a2 2 0 012-2h10a2 2 0 012 2v16z" stroke="currentColor" stroke-width="2"/>
                          </svg>
                        </button>
                      </div>
                    </div>

                    <!-- 图片信息 -->
                    <div class="absolute bottom-2 left-2 right-2 bg-black bg-opacity-50 text-white p-2 rounded text-sm opacity-0 group-hover:opacity-100 transition-opacity">
                      <div class="font-medium truncate">{{ image.prompt }}</div>
                      <div class="text-xs text-gray-300">{{ image.model }} • {{ image.size }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 创作历史 -->
          <div v-if="recentCreations.length > 0" class="bg-white rounded-2xl shadow-lg p-6">
            <h3 class="text-lg font-semibold mb-4">最近创作</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div
                v-for="creation in recentCreations.slice(0, 8)"
                :key="creation.id"
                class="relative group cursor-pointer"
                @click="loadCreation(creation)"
              >
                <img
                  :src="creation.thumbnail"
                  :alt="creation.prompt"
                  class="w-full h-24 object-cover rounded-lg shadow-sm group-hover:shadow-md transition-shadow"
                />
                <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" class="text-white">
                    <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" fill="currentColor"/>
                    <path d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片查看器 -->
    <div v-if="showImageViewer" class="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center">
      <div class="relative max-w-4xl max-h-full p-4">
        <img
          :src="selectedImage?.url"
          :alt="selectedImage?.prompt"
          class="max-w-full max-h-full object-contain rounded-lg"
        />

        <!-- 关闭按钮 -->
        <button
          @click="showImageViewer = false"
          class="absolute top-4 right-4 p-2 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full text-white transition-colors"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2"/>
          </svg>
        </button>

        <!-- 导航按钮 -->
        <button
          v-if="currentImageIndex > 0"
          @click="prevImage"
          class="absolute left-4 top-1/2 transform -translate-y-1/2 p-2 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full text-white transition-colors"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M15 18l-6-6 6-6" stroke="currentColor" stroke-width="2"/>
          </svg>
        </button>

        <button
          v-if="currentImageIndex < generatedImages.length - 1"
          @click="nextImage"
          class="absolute right-4 top-1/2 transform -translate-y-1/2 p-2 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full text-white transition-colors"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M9 18l6-6-6-6" stroke="currentColor" stroke-width="2"/>
          </svg>
        </button>

        <!-- 图片信息 -->
        <div class="absolute bottom-4 left-4 right-4 bg-black bg-opacity-50 text-white p-4 rounded-lg">
          <h3 class="font-semibold mb-2">{{ selectedImage?.prompt }}</h3>
          <div class="text-sm text-gray-300">
            <span>模型: {{ selectedImage?.model }}</span> •
            <span>尺寸: {{ selectedImage?.size }}</span> •
            <span>风格: {{ selectedImage?.style }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 历史记录对话框 -->
    <el-dialog v-model="showHistory" title="创作历史" width="80%" max-width="1000px">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
        <div
          v-for="item in creationHistory"
          :key="item.id"
          class="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
          @click="loadCreation(item); showHistory = false"
        >
          <img
            :src="item.thumbnail"
            :alt="item.prompt"
            class="w-full h-32 object-cover rounded mb-3"
          />
          <h4 class="font-medium text-sm mb-2 line-clamp-2">{{ item.prompt }}</h4>
          <p class="text-xs text-gray-500">{{ formatDate(item.createdAt) }}</p>
        </div>
      </div>
    </el-dialog>

    <!-- 我的作品对话框 -->
    <el-dialog v-model="showMyWorks" title="我的作品" width="80%" max-width="1000px">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
        <div
          v-for="work in myWorks"
          :key="work.id"
          class="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
          @click="loadCreation(work); showMyWorks = false"
        >
          <img
            :src="work.url"
            :alt="work.prompt"
            class="w-full h-32 object-cover rounded mb-3"
          />
          <h4 class="font-medium text-sm mb-2 line-clamp-2">{{ work.prompt }}</h4>
          <p class="text-xs text-gray-500">保存于 {{ formatDate(work.savedAt) }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'

// 响应式数据
const prompt = ref('')
const negativePrompt = ref('')
const selectedModel = ref('flux')
const selectedSize = ref('1024x1024')
const selectedStyle = ref('realistic')
const quality = ref(7)
const creativity = ref(5)
const isGenerating = ref(false)
const generationProgress = ref(0)
const generationStatus = ref('')
const showPromptHelper = ref(false)
const showImageViewer = ref(false)
const selectedImage = ref(null)
const currentImageIndex = ref(0)
const showHistory = ref(false)
const showMyWorks = ref(false)

// 生成的图片
const generatedImages = ref([])
const recentCreations = ref([])
const creationHistory = ref([])
const myWorks = ref([])

// 配置选项
const quickTemplates = ref([
  { id: 1, name: '人物肖像', icon: '👤', prompt: '一个美丽的人物肖像，细腻的面部特征，柔和的光线' },
  { id: 2, name: '自然风景', icon: '🏔️', prompt: '壮丽的自然风景，山川河流，蓝天白云' },
  { id: 3, name: '抽象艺术', icon: '🎨', prompt: '抽象艺术作品，丰富的色彩，流动的线条' },
  { id: 4, name: '科幻场景', icon: '🚀', prompt: '未来科幻场景，高科技建筑，霓虹灯光' },
  { id: 5, name: '动物世界', icon: '🦁', prompt: '野生动物，自然栖息地，生动的表情' },
  { id: 6, name: '建筑设计', icon: '🏛️', prompt: '现代建筑设计，几何线条，光影效果' }
])

const aiModels = ref([
  { id: 'flux', name: 'Flux', description: '最新稳定扩散模型，质量最高' },
  { id: 'turbo', name: 'Turbo', description: '快速生成模型，速度最快' },
  { id: 'flux-realism', name: 'Flux Realism', description: '专注真实感的模型' },
  { id: 'flux-anime', name: 'Flux Anime', description: '动漫风格专用模型' },
  { id: 'flux-3d', name: 'Flux 3D', description: '3D渲染风格模型' }
])

const imageSizes = ref([
  { label: '正方形', value: '1024x1024' },
  { label: '横屏', value: '1792x1024' },
  { label: '竖屏', value: '1024x1792' },
  { label: '宽屏', value: '1920x1080' }
])

const artStyles = ref([
  { id: 'realistic', name: '写实风格' },
  { id: 'cartoon', name: '卡通风格' },
  { id: 'anime', name: '动漫风格' },
  { id: 'oil-painting', name: '油画风格' },
  { id: 'watercolor', name: '水彩风格' },
  { id: 'sketch', name: '素描风格' }
])

const promptSuggestions = ref([
  '高质量', '4K分辨率', '电影级光效', '细节丰富',
  '柔和光线', '暖色调', '冷色调', '梦幻效果',
  '写实风格', '艺术风格', '复古风格', '现代风格'
])

// 方法
const applyTemplate = (template) => {
  prompt.value = template.prompt
  ElMessage.success(`已应用模板：${template.name}`)
}

const addToPrompt = (suggestion) => {
  if (prompt.value) {
    prompt.value += ', ' + suggestion
  } else {
    prompt.value = suggestion
  }
}

const generateImage = async () => {
  if (!prompt.value.trim()) {
    ElMessage.warning('请输入创作描述')
    return
  }

  isGenerating.value = true
  generationProgress.value = 0
  generatedImages.value = []

  try {
    // 导入Pollinations API
    const { generateImage: pollinationsGenerate } = await import('@/services/pollinationsApi.js')

    // 更新进度：开始生成
    generationProgress.value = 10
    generationStatus.value = '正在分析提示词...'
    await new Promise(resolve => setTimeout(resolve, 500))

    generationProgress.value = 30
    generationStatus.value = '连接AI模型...'
    await new Promise(resolve => setTimeout(resolve, 500))

    generationProgress.value = 50
    generationStatus.value = '生成图像中...'

    // 解析尺寸
    const [width, height] = selectedSize.value.split('x').map(Number)

    // 构建完整的提示词
    let fullPrompt = prompt.value
    if (negativePrompt.value.trim()) {
      fullPrompt += ` --negative ${negativePrompt.value}`
    }
    if (selectedStyle.value !== 'realistic') {
      fullPrompt += ` --style ${selectedStyle.value}`
    }

    // 调用Pollinations API生成图像
    const result = await pollinationsGenerate(fullPrompt, {
      width: width,
      height: height,
      model: selectedModel.value,
      enhance: quality.value > 5,
      safe: true,
      seed: Math.floor(Math.random() * 1000000)
    })

    generationProgress.value = 90
    generationStatus.value = '最终处理...'
    await new Promise(resolve => setTimeout(resolve, 500))

    if (result.success) {
      const generatedImage = {
        id: Date.now(),
        url: result.imageUrl,
        directUrl: result.directUrl,
        prompt: prompt.value,
        model: selectedModel.value,
        size: selectedSize.value,
        style: selectedStyle.value,
        seed: result.seed,
        createdAt: new Date()
      }

      generatedImages.value = [generatedImage]

      // 添加到历史记录
      const creation = {
        id: Date.now(),
        prompt: prompt.value,
        negativePrompt: negativePrompt.value,
        model: selectedModel.value,
        size: selectedSize.value,
        style: selectedStyle.value,
        quality: quality.value,
        creativity: creativity.value,
        images: [generatedImage],
        thumbnail: generatedImage.url,
        createdAt: new Date()
      }

      // 添加到最近创作，确保不重复
      const existingIndex = recentCreations.value.findIndex(c => c.id === creation.id)
      if (existingIndex === -1) {
        recentCreations.value.unshift(creation)
        // 限制最近创作数量为8个
        if (recentCreations.value.length > 8) {
          recentCreations.value = recentCreations.value.slice(0, 8)
        }
      }

      // 添加到历史记录，确保不重复
      const historyIndex = creationHistory.value.findIndex(c => c.id === creation.id)
      if (historyIndex === -1) {
        creationHistory.value.unshift(creation)
      }

      // 保存到本地存储
      try {
        localStorage.setItem('ai-creative-history', JSON.stringify(creationHistory.value))
      } catch (error) {
        console.warn('保存历史数据失败:', error)
      }

      generationProgress.value = 100
      generationStatus.value = '生成完成！'

      ElNotification({
        title: '创作完成',
        message: '您的AI艺术作品已生成完成！',
        type: 'success',
        duration: 3000
      })
    } else {
      throw new Error(result.error || '图像生成失败')
    }
  } catch (error) {
    console.error('生成图像错误:', error)
    ElMessage.error(`图像生成失败: ${error.message}`)
    ElNotification({
      title: '生成失败',
      message: '图像生成过程中出现错误，请重试',
      type: 'error',
      duration: 5000
    })
  } finally {
    isGenerating.value = false
  }
}

const openImageViewer = (image, index) => {
  selectedImage.value = image
  currentImageIndex.value = index
  showImageViewer.value = true
}

const nextImage = () => {
  if (currentImageIndex.value < generatedImages.value.length - 1) {
    currentImageIndex.value++
    selectedImage.value = generatedImages.value[currentImageIndex.value]
  }
}

const prevImage = () => {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--
    selectedImage.value = generatedImages.value[currentImageIndex.value]
  }
}

const downloadImage = async (image) => {
  const { downloadImage: downloadUtil } = await import('@/utils/download.js')
  await downloadUtil(image.url || image.imageUrl, `ai-generated-${Date.now()}.png`)
}

const downloadAll = () => {
  ElMessage.success(`开始下载 ${generatedImages.value.length} 张图片`)
}

const shareImage = (image) => {
  // 模拟分享
  navigator.clipboard.writeText(window.location.href)
  ElMessage.success('分享链接已复制到剪贴板')
}

const saveToGallery = (image) => {
  myWorks.value.unshift({
    id: Date.now(),
    ...image,
    savedAt: new Date()
  })
  ElMessage.success('已保存到我的作品')
}

const loadCreation = (creation) => {
  prompt.value = creation.prompt
  negativePrompt.value = creation.negativePrompt || ''
  selectedModel.value = creation.model
  selectedSize.value = creation.size
  selectedStyle.value = creation.style
  quality.value = creation.quality || 7
  creativity.value = creation.creativity || 5
  
  if (creation.images) {
    generatedImages.value = creation.images
  }
  
  ElMessage.success('已加载创作参数')
}

// 清除历史数据（开发调试用）
const clearHistory = () => {
  recentCreations.value = []
  creationHistory.value = []
  localStorage.removeItem('ai-creative-history')
  ElMessage.success('历史数据已清除')
}

// 在开发环境下暴露清除函数到全局，方便调试
if (import.meta.env.DEV) {
  window.clearAIHistory = clearHistory
}

// 清理重复数据的辅助函数
const removeDuplicates = (array, key = 'id') => {
  return array.filter((item, index, self) =>
    index === self.findIndex(i => i[key] === item[key])
  )
}

// 生命周期
onMounted(() => {
  // 加载历史数据
  loadHistoryData()

  // 清理可能存在的重复数据
  recentCreations.value = removeDuplicates(recentCreations.value)
  creationHistory.value = removeDuplicates(creationHistory.value)
})

const formatDate = (date) => {
  const now = new Date()
  const diff = now - new Date(date)
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  if (days < 30) return `${Math.floor(days / 7)}周前`
  return new Date(date).toLocaleDateString()
}

const loadHistoryData = () => {
  // 从本地存储加载历史数据
  try {
    const savedCreations = localStorage.getItem('ai-creative-history')
    if (savedCreations) {
      const parsedCreations = JSON.parse(savedCreations)
      // 确保数据结构正确并去重
      const uniqueCreations = parsedCreations.filter((creation, index, self) =>
        index === self.findIndex(c => c.id === creation.id)
      )
      recentCreations.value = uniqueCreations.slice(0, 8) // 只显示最近8个
      creationHistory.value = uniqueCreations
    }
  } catch (error) {
    console.warn('加载历史数据失败:', error)
    // 如果加载失败，初始化为空数组
    recentCreations.value = []
    creationHistory.value = []
  }
}
</script>

<style scoped>
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #8b5cf6;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-webkit-slider-track {
  height: 8px;
  border-radius: 4px;
  background: #e5e7eb;
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #8b5cf6;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-track {
  height: 8px;
  border-radius: 4px;
  background: #e5e7eb;
  border: none;
}
</style>
