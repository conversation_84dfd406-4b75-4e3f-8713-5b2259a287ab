# 浏览器Token缓存完整指南

## 概述

在没有服务器的前端应用中，Token的安全存储和可靠恢复是用户体验的关键。本项目提供了完整的浏览器Token缓存解决方案，支持多种缓存方式和智能降级策略。

## 🎯 解决的问题

1. **登录状态丢失**：页面刷新后需要重新登录
2. **缓存不可靠**：单一缓存方式可能失效
3. **兼容性问题**：不同浏览器对存储API的支持不同
4. **性能优化**：选择最适合的缓存策略

## 🛠️ 支持的缓存方式

### 1. localStorage（推荐）
```javascript
import { localStorageCache } from '@/utils/tokenCacheManager'

// 保存Token
await localStorageCache.set(token, { 
  expiresAt: Date.now() + 30 * 24 * 60 * 60 * 1000 // 30天
})

// 读取Token
const token = await localStorageCache.get()

// 删除Token
await localStorageCache.remove()
```

**特点：**
- ✅ 持久化存储，关闭浏览器后仍然存在
- ✅ 同步API，使用简单
- ✅ 广泛支持，兼容性好
- ❌ 同域名下所有标签页共享
- ❌ 存储大小限制（通常5-10MB）

### 2. sessionStorage
```javascript
import { sessionStorageCache } from '@/utils/tokenCacheManager'

await sessionStorageCache.set(token)
const token = await sessionStorageCache.get()
```

**特点：**
- ✅ 标签页隔离，安全性更高
- ✅ 关闭标签页自动清除
- ❌ 刷新页面会丢失（不适合持久化登录）

### 3. IndexedDB
```javascript
import { indexedDBCache } from '@/utils/tokenCacheManager'

await indexedDBCache.set(token, { expiresAt: Date.now() + 86400000 })
const token = await indexedDBCache.get()
```

**特点：**
- ✅ 存储容量大（通常几百MB到几GB）
- ✅ 支持复杂数据结构
- ✅ 事务支持，数据一致性好
- ❌ 异步操作，相对复杂
- ❌ 部分旧浏览器不支持

### 4. Cookie
```javascript
import { cookieCache } from '@/utils/tokenCacheManager'

await cookieCache.set(token, { expiresAt: Date.now() + 86400000 })
const token = await cookieCache.get()
```

**特点：**
- ✅ 自动发送到服务器
- ✅ 支持过期时间和域名控制
- ✅ 支持HttpOnly和Secure标志
- ❌ 大小限制（4KB）
- ❌ 每次请求都会发送，影响性能

### 5. 内存缓存
```javascript
import { memoryCache } from '@/utils/tokenCacheManager'

await memoryCache.set(token)
const token = await memoryCache.get()
```

**特点：**
- ✅ 速度最快
- ✅ 不会持久化，安全性高
- ❌ 刷新页面丢失
- ❌ 仅适合临时使用

## 🚀 统一缓存管理器

### 默认缓存管理器
```javascript
import { defaultTokenCache } from '@/utils/tokenCacheManager'

// 保存Token（自动选择最佳缓存方式）
await defaultTokenCache.setToken(token, {
  expiresAt: Date.now() + 30 * 24 * 60 * 60 * 1000
})

// 读取Token（自动降级到备用缓存）
const token = await defaultTokenCache.getToken()

// 删除Token（清除所有缓存）
await defaultTokenCache.removeToken()

// 检查Token是否存在
const hasToken = await defaultTokenCache.hasToken()

// 获取缓存状态
const status = await defaultTokenCache.getCacheStatus()
```

**配置：**
- 主缓存：localStorage
- 备用缓存：sessionStorage, cookie, memory
- 多缓存模式：关闭

### 高可靠性缓存管理器
```javascript
import { reliableTokenCache } from '@/utils/tokenCacheManager'

// 同时保存到多个缓存，确保高可靠性
await reliableTokenCache.setToken(token)
```

**配置：**
- 主缓存：localStorage
- 备用缓存：IndexedDB, Cookie
- 多缓存模式：开启（同时保存到多个缓存）

### 会话缓存管理器
```javascript
import { sessionTokenCache } from '@/utils/tokenCacheManager'

// 仅在当前会话中保存
await sessionTokenCache.setToken(token)
```

**配置：**
- 主缓存：sessionStorage
- 备用缓存：memory
- 多缓存模式：关闭

## 🔧 自定义缓存管理器

```javascript
import { TokenCacheManager } from '@/utils/tokenCacheManager'

// 创建自定义缓存管理器
const customCache = new TokenCacheManager({
  primaryCache: 'indexedDB',           // 主缓存
  fallbackCaches: ['localStorage', 'cookie'], // 备用缓存
  enableMultiCache: true               // 启用多缓存模式
})

// 使用自定义管理器
await customCache.setToken(token)
const token = await customCache.getToken()
```

## 📊 性能对比

| 缓存方式 | 保存速度 | 读取速度 | 存储容量 | 持久化 | 兼容性 |
|---------|---------|---------|---------|--------|--------|
| localStorage | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 5-10MB | ✅ | ⭐⭐⭐⭐⭐ |
| sessionStorage | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 5-10MB | ❌ | ⭐⭐⭐⭐⭐ |
| IndexedDB | ⭐⭐⭐ | ⭐⭐⭐ | 几百MB+ | ✅ | ⭐⭐⭐⭐ |
| Cookie | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 4KB | ✅ | ⭐⭐⭐⭐⭐ |
| Memory | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 无限制 | ❌ | ⭐⭐⭐⭐⭐ |

## 🎯 使用建议

### 生产环境
```javascript
// 推荐使用高可靠性缓存管理器
import { reliableTokenCache } from '@/utils/tokenCacheManager'

// 登录时保存Token
await reliableTokenCache.setToken(token, {
  expiresAt: Date.now() + 7 * 24 * 60 * 60 * 1000 // 7天过期
})

// 应用启动时恢复Token
const token = await reliableTokenCache.getToken()
if (token) {
  // 恢复登录状态
  await restoreUserSession(token)
}
```

### 开发环境
```javascript
// 使用默认缓存管理器，便于调试
import { defaultTokenCache } from '@/utils/tokenCacheManager'

await defaultTokenCache.setToken(token)
```

### 临时会话
```javascript
// 使用会话缓存管理器
import { sessionTokenCache } from '@/utils/tokenCacheManager'

await sessionTokenCache.setToken(token)
```

## 🔒 安全性考虑

### 1. Token过期时间
```javascript
// 设置合理的过期时间
await tokenCache.setToken(token, {
  expiresAt: Date.now() + 24 * 60 * 60 * 1000 // 24小时
})
```

### 2. HTTPS环境下的Cookie安全
```javascript
// Cookie会自动设置Secure标志（HTTPS环境）
await cookieCache.set(token, {
  expiresAt: Date.now() + 86400000,
  secure: true,      // 仅HTTPS传输
  sameSite: 'Strict' // 防止CSRF攻击
})
```

### 3. 定期清理过期数据
```javascript
// 应用启动时清理过期缓存
setInterval(() => {
  tokenCache.cleanup()
}, 60 * 60 * 1000) // 每小时清理一次
```

### 4. Token刷新机制
```javascript
// 检查Token是否即将过期
const token = await tokenCache.getToken()
if (isTokenNearExpiry(token)) {
  const newToken = await refreshToken(token)
  await tokenCache.setToken(newToken)
}
```

## 🧪 测试和调试

### 访问测试页面
访问 `/token-cache-test` 页面可以：
- 测试各种缓存方式的性能
- 查看缓存状态和可用性
- 运行完整的功能测试
- 比较不同缓存方式的可靠性

### 控制台调试
```javascript
// 在浏览器控制台中测试
import { defaultTokenCache } from '@/utils/tokenCacheManager'

// 保存测试Token
await defaultTokenCache.setToken('test_token_123')

// 读取Token
const token = await defaultTokenCache.getToken()
console.log('Token:', token)

// 查看缓存状态
const status = await defaultTokenCache.getCacheStatus()
console.log('缓存状态:', status)
```

## 🔄 迁移指南

### 从简单localStorage迁移
```javascript
// 旧代码
localStorage.setItem('token', token)
const token = localStorage.getItem('token')

// 新代码
import { defaultTokenCache } from '@/utils/tokenCacheManager'
await defaultTokenCache.setToken(token)
const token = await defaultTokenCache.getToken()
```

### 兼容性处理
项目已经更新了 `src/utils/auth.js`，提供了向后兼容的API：

```javascript
import { getToken, setToken, removeToken } from '@/utils/auth'

// 这些函数现在使用新的缓存管理器，但保持API兼容
const token = await getToken()
await setToken(token)
await removeToken()
```

## 📈 监控和维护

### 缓存状态监控
```javascript
// 定期检查缓存健康状态
setInterval(async () => {
  const status = await tokenCache.getCacheStatus()
  console.log('缓存健康检查:', status)
  
  // 如果主缓存不可用，发送警报
  if (!status.localStorage?.available) {
    console.warn('主缓存localStorage不可用')
  }
}, 5 * 60 * 1000) // 每5分钟检查一次
```

### 错误处理和恢复
```javascript
try {
  await tokenCache.setToken(token)
} catch (error) {
  console.error('Token保存失败:', error)
  
  // 尝试降级保存
  try {
    localStorage.setItem('fallback_token', token)
  } catch (fallbackError) {
    console.error('降级保存也失败:', fallbackError)
    // 通知用户或执行其他恢复策略
  }
}
```

## 🎉 总结

通过使用本项目的Token缓存管理系统，您可以：

1. **解决登录状态丢失问题** - 多重缓存确保Token不会丢失
2. **提高应用可靠性** - 智能降级策略应对各种异常情况
3. **优化用户体验** - 快速的Token恢复，无需重复登录
4. **简化开发工作** - 统一的API，无需关心底层实现
5. **支持多种场景** - 从临时会话到长期登录，灵活配置

立即开始使用，让您的应用拥有更可靠的登录状态管理！
