<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片粘贴测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-area {
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        textarea {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
        }
        .file-preview {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
        }
        .file-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 5px 0;
            padding: 5px;
            background: white;
            border-radius: 4px;
        }
        .file-info {
            flex: 1;
        }
        .file-name {
            font-weight: bold;
        }
        .file-size {
            color: #666;
            font-size: 0.9em;
        }
        img {
            max-width: 100px;
            max-height: 100px;
            object-fit: cover;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>图片粘贴功能测试</h1>
    
    <div class="test-area">
        <h3>测试说明：</h3>
        <p>1. 复制一张图片到剪贴板（可以从网页、文件管理器或截图工具复制）</p>
        <p>2. 在下面的文本框中按 Ctrl+V 粘贴</p>
        <p>3. 如果成功，会在下方显示图片预览</p>
        
        <textarea 
            id="testInput" 
            placeholder="在这里按 Ctrl+V 粘贴图片..."
        ></textarea>
        
        <div id="filePreview" class="file-preview" style="display: none;">
            <h4>粘贴的文件：</h4>
            <div id="fileList"></div>
        </div>
    </div>

    <script>
        const testInput = document.getElementById('testInput');
        const filePreview = document.getElementById('filePreview');
        const fileList = document.getElementById('fileList');
        
        testInput.addEventListener('paste', function(event) {
            const clipboardData = event.clipboardData || window.clipboardData;
            const items = clipboardData.items;
            
            console.log('粘贴事件触发，剪贴板项目数量：', items.length);
            
            // 检查是否有图片文件
            for (let i = 0; i < items.length; i++) {
                const item = items[i];
                console.log('项目类型：', item.type);
                
                // 如果是图片类型
                if (item.type.indexOf('image') !== -1) {
                    event.preventDefault(); // 阻止默认粘贴行为
                    
                    const file = item.getAsFile();
                    if (file) {
                        console.log('获取到图片文件：', file);
                        
                        // 创建文件对象
                        const fileObj = {
                            id: Date.now() + Math.random(),
                            name: `粘贴的图片_${new Date().toLocaleString()}.${file.type.split('/')[1]}`,
                            size: file.size,
                            type: file.type,
                            file: file
                        };
                        
                        // 显示文件预览
                        displayFile(fileObj);
                        
                        console.log('图片粘贴成功:', fileObj.name);
                    }
                    break;
                }
            }
        });
        
        function displayFile(fileObj) {
            filePreview.style.display = 'block';
            
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            
            // 创建图片预览
            const reader = new FileReader();
            reader.onload = function(e) {
                fileItem.innerHTML = `
                    <img src="${e.target.result}" alt="预览">
                    <div class="file-info">
                        <div class="file-name">${fileObj.name}</div>
                        <div class="file-size">${formatFileSize(fileObj.size)}</div>
                        <div class="file-type">${fileObj.type}</div>
                    </div>
                `;
            };
            reader.readAsDataURL(fileObj.file);
            
            fileList.appendChild(fileItem);
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
