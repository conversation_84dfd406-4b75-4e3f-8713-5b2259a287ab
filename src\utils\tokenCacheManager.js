/**
 * Token缓存管理器
 * 支持多种浏览器缓存方式，适用于无服务器环境
 */

import { APP_CONFIG } from '@/config'

// 缓存键名
const TOKEN_KEYS = {
  localStorage: APP_CONFIG.storage.tokenKey,
  sessionStorage: 'session_' + APP_CONFIG.storage.tokenKey,
  indexedDB: 'token_db',
  cookie: 'auth_token'
}

/**
 * 1. localStorage - 持久化存储（推荐）
 * 优点：数据持久化，关闭浏览器后仍然存在
 * 缺点：同域名下所有标签页共享，存储大小限制（通常5-10MB）
 */
export const localStorageCache = {
  set(token, options = {}) {
    try {
      const data = {
        token,
        timestamp: Date.now(),
        expiresAt: options.expiresAt || (Date.now() + 30 * 24 * 60 * 60 * 1000), // 默认30天
        ...options
      }
      localStorage.setItem(TOKEN_KEYS.localStorage, JSON.stringify(data))
      console.log('✅ Token已保存到localStorage')
      return true
    } catch (error) {
      console.error('❌ localStorage保存失败:', error)
      return false
    }
  },

  get() {
    try {
      const data = localStorage.getItem(TOKEN_KEYS.localStorage)
      if (!data) return null

      const parsed = JSON.parse(data)
      
      // 检查是否过期
      if (parsed.expiresAt && Date.now() > parsed.expiresAt) {
        this.remove()
        return null
      }

      return parsed.token
    } catch (error) {
      console.error('❌ localStorage读取失败:', error)
      return null
    }
  },

  remove() {
    try {
      localStorage.removeItem(TOKEN_KEYS.localStorage)
      console.log('✅ Token已从localStorage移除')
      return true
    } catch (error) {
      console.error('❌ localStorage移除失败:', error)
      return false
    }
  },

  exists() {
    return !!this.get()
  }
}

/**
 * 2. sessionStorage - 会话存储
 * 优点：标签页隔离，关闭标签页自动清除
 * 缺点：刷新页面会丢失（不适合持久化登录）
 */
export const sessionStorageCache = {
  set(token, options = {}) {
    try {
      const data = {
        token,
        timestamp: Date.now(),
        ...options
      }
      sessionStorage.setItem(TOKEN_KEYS.sessionStorage, JSON.stringify(data))
      console.log('✅ Token已保存到sessionStorage')
      return true
    } catch (error) {
      console.error('❌ sessionStorage保存失败:', error)
      return false
    }
  },

  get() {
    try {
      const data = sessionStorage.getItem(TOKEN_KEYS.sessionStorage)
      if (!data) return null

      const parsed = JSON.parse(data)
      return parsed.token
    } catch (error) {
      console.error('❌ sessionStorage读取失败:', error)
      return null
    }
  },

  remove() {
    try {
      sessionStorage.removeItem(TOKEN_KEYS.sessionStorage)
      console.log('✅ Token已从sessionStorage移除')
      return true
    } catch (error) {
      console.error('❌ sessionStorage移除失败:', error)
      return false
    }
  },

  exists() {
    return !!this.get()
  }
}

/**
 * 3. IndexedDB - 大容量数据库存储
 * 优点：存储容量大，支持复杂数据结构
 * 缺点：异步操作，相对复杂
 */
export const indexedDBCache = {
  dbName: 'TokenCache',
  storeName: 'tokens',
  version: 1,

  async openDB() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version)
      
      request.onerror = () => reject(request.error)
      request.onsuccess = () => resolve(request.result)
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, { keyPath: 'id' })
          store.createIndex('timestamp', 'timestamp', { unique: false })
        }
      }
    })
  },

  async set(token, options = {}) {
    try {
      const db = await this.openDB()
      const transaction = db.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      
      const data = {
        id: 'auth_token',
        token,
        timestamp: Date.now(),
        expiresAt: options.expiresAt || (Date.now() + 30 * 24 * 60 * 60 * 1000),
        ...options
      }
      
      await store.put(data)
      console.log('✅ Token已保存到IndexedDB')
      return true
    } catch (error) {
      console.error('❌ IndexedDB保存失败:', error)
      return false
    }
  },

  async get() {
    try {
      const db = await this.openDB()
      const transaction = db.transaction([this.storeName], 'readonly')
      const store = transaction.objectStore(this.storeName)
      
      return new Promise((resolve, reject) => {
        const request = store.get('auth_token')
        
        request.onerror = () => reject(request.error)
        request.onsuccess = () => {
          const result = request.result
          if (!result) {
            resolve(null)
            return
          }
          
          // 检查是否过期
          if (result.expiresAt && Date.now() > result.expiresAt) {
            this.remove()
            resolve(null)
            return
          }
          
          resolve(result.token)
        }
      })
    } catch (error) {
      console.error('❌ IndexedDB读取失败:', error)
      return null
    }
  },

  async remove() {
    try {
      const db = await this.openDB()
      const transaction = db.transaction([this.storeName], 'readwrite')
      const store = transaction.objectStore(this.storeName)
      
      await store.delete('auth_token')
      console.log('✅ Token已从IndexedDB移除')
      return true
    } catch (error) {
      console.error('❌ IndexedDB移除失败:', error)
      return false
    }
  },

  async exists() {
    const token = await this.get()
    return !!token
  }
}

/**
 * 4. Cookie - 传统方式
 * 优点：自动发送到服务器，支持过期时间
 * 缺点：大小限制（4KB），安全性相对较低
 */
export const cookieCache = {
  set(token, options = {}) {
    try {
      const expires = options.expiresAt ? new Date(options.expiresAt) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      const secure = location.protocol === 'https:' ? '; Secure' : ''
      const sameSite = '; SameSite=Strict'
      
      document.cookie = `${TOKEN_KEYS.cookie}=${encodeURIComponent(token)}; expires=${expires.toUTCString()}; path=/${secure}${sameSite}`
      console.log('✅ Token已保存到Cookie')
      return true
    } catch (error) {
      console.error('❌ Cookie保存失败:', error)
      return false
    }
  },

  get() {
    try {
      const cookies = document.cookie.split(';')
      for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=')
        if (name === TOKEN_KEYS.cookie) {
          return decodeURIComponent(value)
        }
      }
      return null
    } catch (error) {
      console.error('❌ Cookie读取失败:', error)
      return null
    }
  },

  remove() {
    try {
      document.cookie = `${TOKEN_KEYS.cookie}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/`
      console.log('✅ Token已从Cookie移除')
      return true
    } catch (error) {
      console.error('❌ Cookie移除失败:', error)
      return false
    }
  },

  exists() {
    return !!this.get()
  }
}

/**
 * 5. 内存缓存 - 临时存储
 * 优点：速度最快，不会持久化
 * 缺点：刷新页面丢失，仅适合临时使用
 */
export const memoryCache = {
  cache: new Map(),

  set(token, options = {}) {
    try {
      const data = {
        token,
        timestamp: Date.now(),
        expiresAt: options.expiresAt,
        ...options
      }
      this.cache.set('auth_token', data)
      console.log('✅ Token已保存到内存缓存')
      return true
    } catch (error) {
      console.error('❌ 内存缓存保存失败:', error)
      return false
    }
  },

  get() {
    try {
      const data = this.cache.get('auth_token')
      if (!data) return null

      // 检查是否过期
      if (data.expiresAt && Date.now() > data.expiresAt) {
        this.remove()
        return null
      }

      return data.token
    } catch (error) {
      console.error('❌ 内存缓存读取失败:', error)
      return null
    }
  },

  remove() {
    try {
      this.cache.delete('auth_token')
      console.log('✅ Token已从内存缓存移除')
      return true
    } catch (error) {
      console.error('❌ 内存缓存移除失败:', error)
      return false
    }
  },

  exists() {
    return !!this.get()
  },

  clear() {
    this.cache.clear()
  }
}

/**
 * 统一Token缓存管理器
 * 支持多种缓存策略和降级方案
 */
export class TokenCacheManager {
  constructor(options = {}) {
    this.primaryCache = options.primaryCache || 'localStorage'
    this.fallbackCaches = options.fallbackCaches || ['sessionStorage', 'cookie', 'memory']
    this.enableMultiCache = options.enableMultiCache || false // 是否同时使用多种缓存
  }

  // 获取缓存实例
  getCacheInstance(cacheType) {
    const caches = {
      localStorage: localStorageCache,
      sessionStorage: sessionStorageCache,
      indexedDB: indexedDBCache,
      cookie: cookieCache,
      memory: memoryCache
    }
    return caches[cacheType]
  }

  // 保存Token
  async setToken(token, options = {}) {
    const results = []

    try {
      // 主缓存
      const primaryCache = this.getCacheInstance(this.primaryCache)
      if (primaryCache) {
        const result = await primaryCache.set(token, options)
        results.push({ cache: this.primaryCache, success: result })
      }

      // 多缓存模式：同时保存到多个缓存
      if (this.enableMultiCache) {
        for (const cacheType of this.fallbackCaches) {
          const cache = this.getCacheInstance(cacheType)
          if (cache) {
            const result = await cache.set(token, options)
            results.push({ cache: cacheType, success: result })
          }
        }
      }

      const successCount = results.filter(r => r.success).length
      console.log(`✅ Token保存完成: ${successCount}/${results.length} 个缓存成功`)

      return results.some(r => r.success)
    } catch (error) {
      console.error('❌ Token保存失败:', error)
      return false
    }
  }

  // 获取Token（带降级策略）
  async getToken() {
    // 首先尝试主缓存
    try {
      const primaryCache = this.getCacheInstance(this.primaryCache)
      if (primaryCache) {
        const token = await primaryCache.get()
        if (token) {
          console.log(`✅ 从${this.primaryCache}获取Token成功`)
          return token
        }
      }
    } catch (error) {
      console.warn(`⚠️ 主缓存${this.primaryCache}读取失败:`, error)
    }

    // 降级到备用缓存
    for (const cacheType of this.fallbackCaches) {
      try {
        const cache = this.getCacheInstance(cacheType)
        if (cache) {
          const token = await cache.get()
          if (token) {
            console.log(`✅ 从备用缓存${cacheType}获取Token成功`)

            // 恢复到主缓存
            if (this.primaryCache !== cacheType) {
              await this.setToken(token, { source: 'recovery' })
            }

            return token
          }
        }
      } catch (error) {
        console.warn(`⚠️ 备用缓存${cacheType}读取失败:`, error)
      }
    }

    console.log('ℹ️ 所有缓存中都没有找到Token')
    return null
  }

  // 移除Token
  async removeToken() {
    const results = []
    const allCaches = [this.primaryCache, ...this.fallbackCaches]

    for (const cacheType of [...new Set(allCaches)]) {
      try {
        const cache = this.getCacheInstance(cacheType)
        if (cache) {
          const result = await cache.remove()
          results.push({ cache: cacheType, success: result })
        }
      } catch (error) {
        console.warn(`⚠️ 清除${cacheType}缓存失败:`, error)
        results.push({ cache: cacheType, success: false })
      }
    }

    const successCount = results.filter(r => r.success).length
    console.log(`✅ Token清除完成: ${successCount}/${results.length} 个缓存成功`)

    return results.some(r => r.success)
  }

  // 检查Token是否存在
  async hasToken() {
    const token = await this.getToken()
    return !!token
  }

  // 获取缓存状态
  async getCacheStatus() {
    const status = {}
    const allCaches = [this.primaryCache, ...this.fallbackCaches]

    for (const cacheType of [...new Set(allCaches)]) {
      try {
        const cache = this.getCacheInstance(cacheType)
        if (cache) {
          const exists = await cache.exists()
          status[cacheType] = {
            available: true,
            hasToken: exists
          }
        }
      } catch (error) {
        status[cacheType] = {
          available: false,
          error: error.message
        }
      }
    }

    return status
  }

  // 同步所有缓存
  async syncCaches() {
    const token = await this.getToken()
    if (token) {
      await this.setToken(token, { source: 'sync' })
      console.log('✅ 缓存同步完成')
      return true
    }
    return false
  }
}

// 默认实例
export const defaultTokenCache = new TokenCacheManager({
  primaryCache: 'localStorage',
  fallbackCaches: ['sessionStorage', 'cookie', 'memory'],
  enableMultiCache: false
})

// 高可靠性实例（多缓存模式）
export const reliableTokenCache = new TokenCacheManager({
  primaryCache: 'localStorage',
  fallbackCaches: ['indexedDB', 'cookie'],
  enableMultiCache: true
})

// 临时会话实例
export const sessionTokenCache = new TokenCacheManager({
  primaryCache: 'sessionStorage',
  fallbackCaches: ['memory'],
  enableMultiCache: false
})

export default defaultTokenCache
