// 图像生成服务 - 统一管理不同的图像生成模型
import { ElMessage } from 'element-plus'
import { generateImage as pollinationsGenerate } from './pollinationsApi.js'

// 图像生成服务配置
const IMAGE_GENERATION_CONFIG = {
  defaultModel: 'pollinations-default',
  maxRetries: 3,
  timeout: 60000
}

// 图像生成模型映射
const MODEL_PROVIDERS = {
  'pollinations-default': 'pollinations',
  'pollinations-artistic': 'pollinations',
  'pollinations-realistic': 'pollinations'
}

// 图像生成服务类
class ImageGenerationService {
  constructor() {
    this.activeRequests = new Map()
  }

  // 生成图像的主要方法
  async generateImage(prompt, options = {}) {
    const requestId = this.generateRequestId()
    
    try {
      // 验证输入
      if (!prompt || typeof prompt !== 'string' || !prompt.trim()) {
        throw new Error('请提供有效的图像描述')
      }

      // 设置默认选项
      const finalOptions = {
        model: IMAGE_GENERATION_CONFIG.defaultModel,
        width: 512,
        height: 512,
        enhance: false,
        safe: true,
        seed: null,
        ...options
      }

      // 记录请求
      this.activeRequests.set(requestId, {
        prompt,
        options: finalOptions,
        startTime: Date.now()
      })

      // 根据模型选择提供商
      const provider = MODEL_PROVIDERS[finalOptions.model] || 'pollinations'
      
      let result
      switch (provider) {
        case 'pollinations':
          result = await this.generateWithPollinations(prompt, finalOptions)
          break
        default:
          throw new Error(`不支持的图像生成模型: ${finalOptions.model}`)
      }

      // 添加元数据
      if (result.success) {
        result.metadata = {
          requestId,
          provider,
          model: finalOptions.model,
          generatedAt: new Date().toISOString(),
          processingTime: Date.now() - this.activeRequests.get(requestId).startTime
        }
      }

      return result
    } catch (error) {
      console.error('图像生成失败:', error)
      return {
        success: false,
        error: error.message,
        prompt
      }
    } finally {
      // 清理请求记录
      this.activeRequests.delete(requestId)
    }
  }

  // 使用Pollinations生成图像
  async generateWithPollinations(prompt, options) {
    try {
      // 构建Pollinations特定的选项
      const pollinationsOptions = {
        width: options.width,
        height: options.height,
        seed: options.seed,
        enhance: options.enhance,
        safe: options.safe
      }

      // 调用Pollinations API
      const result = await pollinationsGenerate(prompt, pollinationsOptions)
      
      if (result.success) {
        return {
          success: true,
          imageUrl: result.imageUrl,
          imageBlob: result.imageBlob,
          directUrl: result.directUrl,
          prompt: result.prompt,
          seed: result.seed,
          options: pollinationsOptions,
          provider: 'pollinations'
        }
      } else {
        throw new Error(result.error || 'Pollinations图像生成失败')
      }
    } catch (error) {
      throw new Error(`Pollinations生成失败: ${error.message}`)
    }
  }

  // 批量生成图像
  async generateMultipleImages(prompts, options = {}) {
    if (!Array.isArray(prompts) || prompts.length === 0) {
      throw new Error('请提供有效的提示词数组')
    }

    const results = []
    const errors = []

    for (let i = 0; i < prompts.length; i++) {
      try {
        const result = await this.generateImage(prompts[i], {
          ...options,
          seed: options.seed ? options.seed + i : null // 为每个图像使用不同的种子
        })
        
        if (result.success) {
          results.push(result)
        } else {
          errors.push({ index: i, prompt: prompts[i], error: result.error })
        }
      } catch (error) {
        errors.push({ index: i, prompt: prompts[i], error: error.message })
      }
    }

    return {
      success: results.length > 0,
      results,
      errors,
      total: prompts.length,
      successCount: results.length,
      errorCount: errors.length
    }
  }

  // 图像风格转换
  async styleTransfer(imageUrl, style, options = {}) {
    // 构建风格转换提示词
    const stylePrompts = {
      'watercolor': '水彩画风格',
      'oil_painting': '油画风格',
      'anime': '动漫风格',
      'sketch': '素描风格',
      'realistic': '写实风格',
      'abstract': '抽象艺术风格'
    }

    const stylePrompt = stylePrompts[style] || style
    const prompt = `将图像转换为${stylePrompt}`

    return await this.generateImage(prompt, {
      ...options,
      referenceImage: imageUrl
    })
  }

  // 图像增强
  async enhanceImage(imageUrl, options = {}) {
    const enhancePrompt = '高质量, 超高清, 细节丰富, 专业摄影, 完美构图'
    
    return await this.generateImage(enhancePrompt, {
      ...options,
      referenceImage: imageUrl,
      enhance: true
    })
  }

  // 获取活跃请求数量
  getActiveRequestCount() {
    return this.activeRequests.size
  }

  // 取消所有活跃请求
  cancelAllRequests() {
    this.activeRequests.clear()
  }

  // 生成请求ID
  generateRequestId() {
    return `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 验证图像URL
  async validateImageUrl(url) {
    try {
      const response = await fetch(url, { method: 'HEAD' })
      return response.ok && response.headers.get('content-type')?.startsWith('image/')
    } catch {
      return false
    }
  }

  // 下载图像
  async downloadImage(imageUrl, filename) {
    try {
      // 尝试多种下载方式以确保兼容性
      const downloadFilename = filename || `ai_image_${Date.now()}.png`

      // 方法1：使用fetch获取blob（推荐方式）
      try {
        const response = await fetch(imageUrl, {
          mode: 'cors',
          credentials: 'omit',
          headers: {
            'Accept': 'image/*'
          }
        })

        if (response.ok) {
          const blob = await response.blob()
          const url = window.URL.createObjectURL(blob)

          const link = document.createElement('a')
          link.href = url
          link.download = downloadFilename
          link.style.display = 'none'
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)

          // 延迟清理URL对象，确保下载完成
          setTimeout(() => {
            window.URL.revokeObjectURL(url)
          }, 1000)

          return { success: true, method: 'fetch' }
        }
      } catch (fetchError) {
        console.warn('Fetch方式下载失败，尝试直接下载:', fetchError)
      }

      // 方法2：直接下载（适用于同域或支持CORS的图片）
      try {
        const link = document.createElement('a')
        link.href = imageUrl
        link.download = downloadFilename
        link.target = '_blank'
        link.rel = 'noopener noreferrer'
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        return { success: true, method: 'direct' }
      } catch (directError) {
        console.warn('直接下载失败:', directError)
      }

      // 方法3：在新窗口打开（备用方案）
      window.open(imageUrl, '_blank', 'noopener,noreferrer')
      return { success: true, method: 'newWindow', message: '已在新窗口打开，请右键保存' }

    } catch (error) {
      console.error('下载图像失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 获取图像信息
  async getImageInfo(imageUrl) {
    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => {
        resolve({
          width: img.naturalWidth,
          height: img.naturalHeight,
          aspectRatio: img.naturalWidth / img.naturalHeight,
          size: `${img.naturalWidth}×${img.naturalHeight}`
        })
      }
      img.onerror = () => {
        resolve(null)
      }
      img.src = imageUrl
    })
  }
}

// 创建单例实例
const imageGenerationService = new ImageGenerationService()

// 导出服务实例和便捷方法
export default imageGenerationService

export const {
  generateImage,
  generateMultipleImages,
  styleTransfer,
  enhanceImage,
  downloadImage,
  getImageInfo,
  validateImageUrl
} = imageGenerationService

// 导出配置
export { IMAGE_GENERATION_CONFIG, MODEL_PROVIDERS }
