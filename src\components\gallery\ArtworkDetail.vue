<template>
  <el-dialog
    v-model="visible"
    title="作品详情"
    width="90%"
    max-width="1200px"
    :before-close="handleClose"
    class="artwork-detail-dialog"
  >
    <div v-if="artwork" class="artwork-detail">
      <!-- 作品展示区 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- 左侧：作品图片 -->
        <div class="artwork-image-section">
          <div class="relative bg-gray-100 rounded-lg overflow-hidden">
            <img
              :src="artwork.imageUrl"
              :alt="artwork.title"
              class="w-full h-auto max-h-96 object-contain"
              @load="imageLoaded = true"
            />
            <div v-if="!imageLoaded" class="absolute inset-0 flex items-center justify-center">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="flex items-center justify-between mt-4">
            <div class="flex items-center space-x-4">
              <button
                @click="handleLike"
                class="flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors font-medium shadow-sm"
                :class="artwork.isLiked ? 'bg-red-50 text-red-600 border border-red-200 hover:bg-red-100' : 'bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100'"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
                    :fill="artwork.isLiked ? 'currentColor' : 'none'"
                    stroke="currentColor"
                    stroke-width="2"
                  />
                </svg>
                <span>{{ artwork.likes }}</span>
              </button>

              <button
                @click="handleShare"
                class="flex items-center space-x-2 px-4 py-2 bg-gray-50 text-gray-600 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors font-medium shadow-sm"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8" stroke="currentColor" stroke-width="2"/>
                  <polyline points="16,6 12,2 8,6" stroke="currentColor" stroke-width="2"/>
                  <line x1="12" y1="2" x2="12" y2="15" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>分享</span>
              </button>
            </div>
            
            <button
              @click="handleDownload"
              class="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors font-medium shadow-sm"
            >
              下载
            </button>
          </div>
        </div>
        
        <!-- 右侧：作品信息 -->
        <div class="artwork-info-section">
          <div class="space-y-6">
            <!-- 标题和基本信息 -->
            <div>
              <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ artwork.title }}</h2>
              <div class="flex items-center space-x-4 text-sm text-gray-600">
                <span>{{ artwork.category }}</span>
                <span>•</span>
                <span>{{ formatDate(artwork.createdAt) }}</span>
                <span>•</span>
                <span>{{ artwork.views }} 次浏览</span>
              </div>
            </div>
            
            <!-- 作者信息 -->
            <div class="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg">
              <img
                :src="artwork.author.avatar"
                :alt="artwork.author.name"
                class="w-12 h-12 rounded-full"
              />
              <div>
                <h3 class="font-semibold text-gray-900">{{ artwork.author.name }}</h3>
                <p class="text-sm text-gray-600">{{ artwork.author.level }}</p>
              </div>
            </div>
            
            <!-- 描述 -->
            <div v-if="artwork.description">
              <h3 class="font-semibold text-gray-900 mb-2">作品描述</h3>
              <p class="text-gray-700 leading-relaxed">{{ artwork.description }}</p>
            </div>
            
            <!-- 标签 -->
            <div v-if="artwork.tags && artwork.tags.length">
              <h3 class="font-semibold text-gray-900 mb-2">标签</h3>
              <div class="flex flex-wrap gap-2">
                <span
                  v-for="tag in artwork.tags"
                  :key="tag"
                  class="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm"
                >
                  {{ tag }}
                </span>
              </div>
            </div>
            
            <!-- 技术参数 -->
            <div v-if="artwork.parameters">
              <h3 class="font-semibold text-gray-900 mb-2">生成参数</h3>
              <div class="bg-gray-50 rounded-lg p-4 space-y-2 text-sm">
                <div v-if="artwork.parameters.prompt">
                  <span class="font-medium">提示词：</span>
                  <span class="text-gray-700">{{ artwork.parameters.prompt }}</span>
                </div>
                <div v-if="artwork.parameters.model">
                  <span class="font-medium">模型：</span>
                  <span class="text-gray-700">{{ artwork.parameters.model }}</span>
                </div>
                <div v-if="artwork.parameters.resolution">
                  <span class="font-medium">分辨率：</span>
                  <span class="text-gray-700">{{ artwork.parameters.resolution }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 评论区 -->
      <div class="mt-8 border-t pt-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">评论 ({{ comments.length }})</h3>
        
        <!-- 评论输入 -->
        <div class="mb-6">
          <el-input
            v-model="newComment"
            type="textarea"
            :rows="3"
            placeholder="写下你的评论..."
            class="mb-3"
          />
          <div class="flex justify-end">
            <button
              @click="handleComment"
              :disabled="!newComment.trim()"
              class="px-6 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
            >
              发表评论
            </button>
          </div>
        </div>
        
        <!-- 评论列表 -->
        <div class="space-y-4">
          <div
            v-for="comment in comments"
            :key="comment.id"
            class="flex space-x-3 p-4 bg-gray-50 rounded-lg"
          >
            <img
              :src="comment.user.avatar"
              :alt="comment.user.name"
              class="w-8 h-8 rounded-full"
            />
            <div class="flex-1">
              <div class="flex items-center space-x-2 mb-1">
                <span class="font-medium text-gray-900">{{ comment.user.name }}</span>
                <span class="text-sm text-gray-500">{{ formatDate(comment.createdAt) }}</span>
              </div>
              <p class="text-gray-700">{{ comment.content }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div v-else class="flex items-center justify-center h-64">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  artworkId: {
    type: [String, Number],
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'like', 'comment'])

// 响应式数据
const imageLoaded = ref(false)
const newComment = ref('')
const comments = ref([])

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 模拟作品数据
const artwork = ref(null)

// 监听作品ID变化
watch(() => props.artworkId, (newId) => {
  if (newId) {
    loadArtworkDetail(newId)
  }
}, { immediate: true })

// 加载作品详情
const loadArtworkDetail = async (id) => {
  try {
    // 模拟API调用
    artwork.value = {
      id,
      title: '梦幻星空',
      description: '这是一幅充满想象力的AI生成艺术作品，展现了宇宙的神秘与美丽。',
      imageUrl: 'https://picsum.photos/800/600?random=' + id,
      category: '科幻艺术',
      createdAt: new Date(),
      views: 1234,
      likes: 89,
      isLiked: false,
      author: {
        name: 'AI艺术家',
        avatar: 'https://picsum.photos/100/100?random=user' + id,
        level: '专业创作者'
      },
      tags: ['科幻', '星空', '梦幻', 'AI艺术'],
      parameters: {
        prompt: 'A beautiful starry night sky with nebula and galaxies',
        model: 'DALL-E 3',
        resolution: '1024x1024'
      }
    }
    
    // 模拟评论数据
    comments.value = [
      {
        id: 1,
        content: '太美了！这个作品真的很有创意。',
        user: {
          name: '艺术爱好者',
          avatar: 'https://picsum.photos/100/100?random=comment1'
        },
        createdAt: new Date(Date.now() - 86400000)
      }
    ]
  } catch (error) {
    console.error('加载作品详情失败:', error)
    ElMessage.error('加载作品详情失败')
  }
}

// 事件处理
const handleClose = () => {
  visible.value = false
}

const handleLike = () => {
  if (artwork.value) {
    artwork.value.isLiked = !artwork.value.isLiked
    artwork.value.likes += artwork.value.isLiked ? 1 : -1
    emit('like', artwork.value)
  }
}

const handleShare = () => {
  // 实现分享功能
  ElMessage.success('分享链接已复制到剪贴板')
}

const handleDownload = async () => {
  if (artwork.value?.imageUrl) {
    const { downloadImage } = await import('@/utils/download.js')
    await downloadImage(artwork.value.imageUrl, `artwork-${artwork.value.id || Date.now()}.png`)
  } else {
    ElMessage.error('图片地址不存在')
  }
}

const handleComment = () => {
  if (newComment.value.trim()) {
    const comment = {
      id: Date.now(),
      content: newComment.value,
      user: {
        name: '当前用户',
        avatar: 'https://picsum.photos/100/100?random=currentuser'
      },
      createdAt: new Date()
    }
    comments.value.unshift(comment)
    emit('comment', comment)
    newComment.value = ''
    ElMessage.success('评论发表成功')
  }
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}
</script>

<style scoped>
.artwork-detail-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.artwork-detail {
  max-height: 80vh;
  overflow-y: auto;
}

/* 确保按钮样式不被覆盖 */
button {
  border: none !important;
  outline: none !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}

button:focus {
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(147, 51, 234, 0.1) !important;
}

button:disabled {
  cursor: not-allowed !important;
  opacity: 0.5 !important;
}

/* 自定义滚动条 */
.artwork-detail::-webkit-scrollbar {
  width: 6px;
}

.artwork-detail::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.artwork-detail::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.artwork-detail::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
