# Claude API 集成说明

## 概述

本项目已成功集成 Claude API，支持多个 Claude 模型，包括：

- <PERSON> 3.5 Sonnet
- Claude 3.7 Sonnet  
- Claude 4 Sonnet
- DeepSeek R1
- OpenAI GPT-4.1

## 集成的文件

### 1. 核心服务文件

- `src/services/claudeApi.js` - Claude API 服务类
- `src/services/chatService.js` - 更新了统一聊天服务以支持 Claude

### 2. 配置文件

- `src/config/apiConfig.js` - 添加了 Claude API 配置
- `src/components/ApiKeyConfig.vue` - 更新了 API 配置界面

### 3. 测试文件

- `src/components/ClaudeTest.vue` - Claude API 测试组件

## 使用方法

### 1. 配置 API 密钥

#### 方法一：通过配置界面
1. 访问应用的 API 配置页面
2. 找到 "Claude (第三方)" 提供商
3. 点击配置按钮
4. 输入您的 API 密钥：`••••••••••••••••`
5. API 地址默认为：`https://wd-fl2api.deno.dev/v1/chat/completions`
6. 点击"测试连接"验证配置
7. 保存配置

#### 方法二：直接设置 localStorage
```javascript
localStorage.setItem('claude_api_key', 'your-api-key-here')
localStorage.setItem('claude_base_url', 'https://wd-fl2api.deno.dev/v1/chat/completions')
```

### 2. 在聊天中使用

配置完成后，您可以在聊天界面中选择以下 Claude 模型：

- `claude-3.5-sonnet` - 平衡性能与效率
- `claude-3.7-sonnet` - 增强版本
- `claude-4-sonnet` - 最新最强版本
- `deepseek-r1` - 推理优化模型
- `openai-gpt-4.1` - 最新 GPT 模型

### 3. 编程方式使用

```javascript
import claudeApi from '@/services/claudeApi'

// 设置 API 密钥
claudeApi.setApiKey('your-api-key')

// 发送聊天请求
const result = await claudeApi.createChatCompletion({
  model: 'claude-3.5-sonnet',
  messages: [
    { role: 'user', content: '你好，请介绍一下你自己。' }
  ],
  max_tokens: 1000,
  temperature: 0.7
})

if (result.success) {
  console.log('回复:', result.data.choices[0].message.content)
} else {
  console.error('错误:', result.message)
}
```

### 4. 流式聊天

```javascript
await claudeApi.createChatCompletionStream(
  {
    model: 'claude-3.5-sonnet',
    messages: [{ role: 'user', content: '写一首诗' }],
    max_tokens: 1000
  },
  // onMessage 回调
  (content) => {
    console.log('收到内容:', content)
  },
  // onError 回调
  (error) => {
    console.error('流式错误:', error)
  },
  // onComplete 回调
  () => {
    console.log('流式完成')
  }
)
```

## API 特性

### 支持的功能

- ✅ 文本聊天
- ✅ 流式响应
- ✅ 多模型支持
- ✅ 错误处理
- ✅ 连接测试
- ✅ 模型列表获取

### 模型特点

| 模型 | 最大 Token | 特点 |
|------|-----------|------|
| claude-3.5-sonnet | 200,000 | 平衡性能与效率 |
| claude-3.7-sonnet | 200,000 | 增强版本 |
| claude-4-sonnet | 200,000 | 最新最强版本 |
| deepseek-r1 | 128,000 | 推理优化模型 |
| openai-gpt-4.1 | 128,000 | 最新 GPT 模型 |

## 错误处理

API 服务包含完整的错误处理机制：

- `401` - API Key 无效或已过期
- `403` - 访问被拒绝，请检查 API Key 权限
- `429` - 请求频率过高，请稍后重试
- `500` - Claude 服务器内部错误
- `503` - Claude 服务暂时不可用

## 测试

使用 `ClaudeTest.vue` 组件可以快速测试 Claude API 集成：

1. 输入 API 密钥
2. 选择模型
3. 输入测试消息
4. 查看响应结果

## 注意事项

1. **API 密钥安全**：请妥善保管您的 API 密钥，不要在代码中硬编码
2. **请求频率**：注意 API 的请求频率限制
3. **Token 限制**：不同模型有不同的 Token 限制
4. **网络连接**：确保网络可以访问 API 地址

## 故障排除

### 常见问题

1. **连接失败**
   - 检查 API 密钥是否正确
   - 检查网络连接
   - 确认 API 地址可访问

2. **请求失败**
   - 检查消息格式是否正确
   - 确认模型名称是否支持
   - 检查 Token 数量是否超限

3. **响应异常**
   - 查看控制台错误信息
   - 检查 API 服务状态
   - 确认请求参数是否合法

如有其他问题，请查看浏览器控制台的详细错误信息。
