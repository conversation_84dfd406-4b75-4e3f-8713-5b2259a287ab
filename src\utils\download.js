/**
 * 通用下载工具函数
 * 解决跨域图片下载、浏览器兼容性等问题
 */

import { ElMessage } from 'element-plus'

/**
 * 下载图片文件
 * @param {string} imageUrl - 图片URL
 * @param {string} filename - 文件名（可选）
 * @param {Object} options - 配置选项
 * @returns {Promise<Object>} 下载结果
 */
export async function downloadImage(imageUrl, filename, options = {}) {
  const {
    showMessage = true,
    timeout = 30000,
    retryCount = 2
  } = options

  if (!imageUrl) {
    if (showMessage) ElMessage.error('图片地址不能为空')
    return { success: false, error: '图片地址不能为空' }
  }

  const downloadFilename = filename || `image_${Date.now()}.png`
  
  if (showMessage) ElMessage.info('正在准备下载...')

  // 尝试多种下载方式
  for (let attempt = 0; attempt <= retryCount; attempt++) {
    try {
      // 方法1：使用fetch获取blob（推荐，支持跨域）
      const result = await downloadWithFetch(imageUrl, downloadFilename, timeout)
      if (result.success) {
        if (showMessage) ElMessage.success('图片下载成功')
        return result
      }
    } catch (fetchError) {
      console.warn(`Fetch下载失败 (尝试 ${attempt + 1}/${retryCount + 1}):`, fetchError)
    }

    // 方法2：直接下载（适用于同域或支持CORS的图片）
    try {
      const result = await downloadDirect(imageUrl, downloadFilename)
      if (result.success) {
        if (showMessage) ElMessage.success('图片下载开始')
        return result
      }
    } catch (directError) {
      console.warn(`直接下载失败 (尝试 ${attempt + 1}/${retryCount + 1}):`, directError)
    }
  }

  // 最后的备用方案：在新窗口打开
  try {
    window.open(imageUrl, '_blank', 'noopener,noreferrer')
    if (showMessage) ElMessage.info('已在新窗口打开图片，请右键保存')
    return { success: true, method: 'newWindow', message: '已在新窗口打开' }
  } catch (error) {
    console.error('所有下载方式都失败:', error)
    if (showMessage) ElMessage.error('下载失败，请稍后重试')
    return { success: false, error: '所有下载方式都失败' }
  }
}

/**
 * 使用fetch方式下载
 */
async function downloadWithFetch(imageUrl, filename, timeout = 30000) {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), timeout)

  try {
    const response = await fetch(imageUrl, {
      mode: 'cors',
      credentials: 'omit',
      headers: {
        'Accept': 'image/*,*/*'
      },
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const blob = await response.blob()
    
    // 验证是否为图片
    if (!blob.type.startsWith('image/')) {
      console.warn('响应不是图片类型:', blob.type)
    }

    const downloadUrl = window.URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename
    link.style.display = 'none'
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    // 延迟清理URL对象，确保下载完成
    setTimeout(() => {
      window.URL.revokeObjectURL(downloadUrl)
    }, 1000)
    
    return { success: true, method: 'fetch', size: blob.size }
  } catch (error) {
    clearTimeout(timeoutId)
    throw error
  }
}

/**
 * 直接下载方式
 */
async function downloadDirect(imageUrl, filename) {
  return new Promise((resolve, reject) => {
    try {
      const link = document.createElement('a')
      link.href = imageUrl
      link.download = filename
      link.target = '_blank'
      link.rel = 'noopener noreferrer'
      link.style.display = 'none'
      
      // 添加事件监听器来检测下载是否开始
      let downloadStarted = false
      
      const handleFocus = () => {
        setTimeout(() => {
          if (!downloadStarted) {
            downloadStarted = true
            resolve({ success: true, method: 'direct' })
          }
        }, 100)
      }
      
      window.addEventListener('focus', handleFocus, { once: true })
      
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      // 如果2秒内没有触发focus事件，认为下载成功
      setTimeout(() => {
        window.removeEventListener('focus', handleFocus)
        if (!downloadStarted) {
          downloadStarted = true
          resolve({ success: true, method: 'direct' })
        }
      }, 2000)
      
    } catch (error) {
      reject(error)
    }
  })
}

/**
 * 下载多个图片（批量下载）
 * @param {Array} imageList - 图片列表 [{url, filename}]
 * @param {Object} options - 配置选项
 */
export async function downloadMultipleImages(imageList, options = {}) {
  const {
    showMessage = true,
    delay = 500 // 每个下载之间的延迟
  } = options

  if (!Array.isArray(imageList) || imageList.length === 0) {
    if (showMessage) ElMessage.error('图片列表不能为空')
    return { success: false, error: '图片列表不能为空' }
  }

  if (showMessage) ElMessage.info(`开始批量下载 ${imageList.length} 张图片...`)

  const results = []
  let successCount = 0

  for (let i = 0; i < imageList.length; i++) {
    const { url, filename } = imageList[i]
    
    try {
      const result = await downloadImage(url, filename, { showMessage: false })
      results.push(result)
      
      if (result.success) {
        successCount++
      }
      
      // 添加延迟避免浏览器限制
      if (i < imageList.length - 1) {
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    } catch (error) {
      results.push({ success: false, error: error.message })
    }
  }

  if (showMessage) {
    if (successCount === imageList.length) {
      ElMessage.success(`批量下载完成，共 ${successCount} 张图片`)
    } else {
      ElMessage.warning(`批量下载完成，成功 ${successCount}/${imageList.length} 张图片`)
    }
  }

  return {
    success: successCount > 0,
    total: imageList.length,
    successCount,
    results
  }
}

/**
 * 检查URL是否可下载
 * @param {string} url - 图片URL
 * @returns {Promise<boolean>}
 */
export async function checkDownloadable(url) {
  try {
    const response = await fetch(url, {
      method: 'HEAD',
      mode: 'cors',
      credentials: 'omit'
    })
    return response.ok
  } catch (error) {
    console.warn('检查下载可用性失败:', error)
    return false
  }
}

/**
 * 获取图片信息
 * @param {string} imageUrl - 图片URL
 * @returns {Promise<Object>} 图片信息
 */
export async function getImageInfo(imageUrl) {
  return new Promise((resolve) => {
    const img = new Image()
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
        aspectRatio: img.naturalWidth / img.naturalHeight,
        size: `${img.naturalWidth}×${img.naturalHeight}`
      })
    }
    img.onerror = () => {
      resolve(null)
    }
    img.src = imageUrl
  })
}

export default {
  downloadImage,
  downloadMultipleImages,
  checkDownloadable,
  getImageInfo
}
