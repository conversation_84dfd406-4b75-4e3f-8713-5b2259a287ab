// 认证相关工具函数
import { APP_CONFIG } from '@/config'
import { defaultTokenCache, reliableTokenCache } from './tokenCacheManager'

const TOKEN_KEY = APP_CONFIG.storage.tokenKey

// 选择缓存策略
const tokenCache = import.meta.env.PROD ? reliableTokenCache : defaultTokenCache

/**
 * 获取token
 */
export async function getToken() {
  try {
    // 优先使用新的缓存管理器
    const token = await tokenCache.getToken()
    if (token) {
      return token
    }

    // 降级到原始localStorage（向后兼容）
    const fallbackToken = localStorage.getItem(TOKEN_KEY)
    if (fallbackToken) {
      console.log('⚠️ 使用降级localStorage获取Token')
      // 迁移到新的缓存系统
      await tokenCache.setToken(fallbackToken, { source: 'migration' })
      return fallbackToken
    }

    return null
  } catch (error) {
    console.error('❌ 获取Token失败:', error)
    // 最终降级
    return localStorage.getItem(TOKEN_KEY)
  }
}

/**
 * 设置token
 */
export async function setToken(token, options = {}) {
  try {
    // 使用新的缓存管理器
    const success = await tokenCache.setToken(token, {
      expiresAt: options.expiresAt || (Date.now() + 30 * 24 * 60 * 60 * 1000), // 默认30天
      ...options
    })

    if (success) {
      return true
    }

    // 降级到localStorage
    console.warn('⚠️ 使用降级localStorage保存Token')
    localStorage.setItem(TOKEN_KEY, token)
    return true
  } catch (error) {
    console.error('❌ 设置Token失败:', error)
    // 最终降级
    localStorage.setItem(TOKEN_KEY, token)
    return false
  }
}

/**
 * 移除token
 */
export async function removeToken() {
  try {
    // 使用新的缓存管理器
    const success = await tokenCache.removeToken()

    // 同时清除原始localStorage（确保完全清除）
    localStorage.removeItem(TOKEN_KEY)

    return success
  } catch (error) {
    console.error('❌ 移除Token失败:', error)
    // 最终降级
    localStorage.removeItem(TOKEN_KEY)
    return false
  }
}

/**
 * 检查token是否存在
 */
export async function hasToken() {
  try {
    return await tokenCache.hasToken()
  } catch (error) {
    console.error('❌ 检查Token存在性失败:', error)
    // 降级检查
    return !!localStorage.getItem(TOKEN_KEY)
  }
}

/**
 * 同步版本的获取token（向后兼容）
 */
export function getTokenSync() {
  console.warn('⚠️ 使用同步版本getTokenSync，建议使用异步版本getToken')
  return localStorage.getItem(TOKEN_KEY)
}

/**
 * 获取缓存状态
 */
export async function getTokenCacheStatus() {
  try {
    return await tokenCache.getCacheStatus()
  } catch (error) {
    console.error('❌ 获取缓存状态失败:', error)
    return {}
  }
}

/**
 * 检查token是否过期
 */
export function isTokenExpired(token) {
  if (!token) return true

  // 确保token是字符串类型
  if (typeof token !== 'string') {
    console.warn('Token不是字符串类型:', typeof token, token)
    return true
  }

  try {
    // 检查是否是JWT格式的token
    if (token.includes('.') && token.split('.').length === 3) {
      // 解析JWT token
      const payload = JSON.parse(atob(token.split('.')[1]))
      const currentTime = Math.floor(Date.now() / 1000)
      return payload.exp < currentTime
    }

    // 检查是否是mock token格式 (mock_token_timestamp_random_exp_expirationTime)
    if (token.startsWith('mock_token_') && token.includes('_exp_')) {
      const parts = token.split('_exp_')
      if (parts.length === 2) {
        const expirationTime = parseInt(parts[1])
        const currentTime = Date.now()
        return currentTime > expirationTime
      }
    }

    // 如果无法识别token格式，默认认为未过期（向后兼容）
    console.warn('无法识别的token格式:', token.substring(0, 20) + '...')
    return false
  } catch (error) {
    console.error('解析token失败:', error)
    return true
  }
}

/**
 * 获取token中的用户信息
 */
export function getTokenUserInfo(token) {
  if (!token) return null

  try {
    // 检查是否是JWT格式的token
    if (token.includes('.') && token.split('.').length === 3) {
      const payload = JSON.parse(atob(token.split('.')[1]))
      return {
        userId: payload.userId,
        username: payload.username,
        role: payload.role,
        exp: payload.exp,
      }
    }

    // 对于mock token，从localStorage获取用户信息
    if (token.startsWith('mock_token_')) {
      const userInfo = localStorage.getItem(APP_CONFIG.storage.userKey)
      if (userInfo) {
        const parsedUserInfo = JSON.parse(userInfo)
        return {
          userId: parsedUserInfo.id,
          username: parsedUserInfo.username,
          role: parsedUserInfo.role,
          exp: null, // mock token的过期时间在token字符串中
        }
      }
    }

    return null
  } catch (error) {
    console.error('解析token用户信息失败:', error)
    return null
  }
}

/**
 * 清除所有认证信息
 */
export function clearAuth() {
  removeToken()
  localStorage.removeItem(APP_CONFIG.storage.userKey)
  localStorage.removeItem(APP_CONFIG.storage.settingsKey)
}
