/**
 * 模型测试工具
 * 用于测试不同模型的可用性
 */

import { generateImageToImage } from '@/services/fuioApi.js'

/**
 * 测试图文生成模型
 */
export async function testImageToImageModel() {
  console.log('🧪 开始测试图文生成模型...')
  
  // 创建一个简单的测试图像（1x1像素的透明PNG）
  const testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA4nEKtAAAAABJRU5ErkJggg=='
  
  const testPrompt = '测试图像生成'
  
  try {
    const result = await generateImageToImage(testPrompt, testImageBase64, {
      aspectRatio: '1:1',
      n: 1
    })
    
    console.log('✅ 模型测试结果:', result)
    return result
  } catch (error) {
    console.error('❌ 模型测试失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 检查模型配置
 */
export function checkModelConfig() {
  console.log('🔍 检查模型配置...')
  
  // 动态导入配置以避免循环依赖
  import('@/services/fuioApi.js').then(module => {
    // 这里无法直接访问 FUIO_CONFIG，因为它不是导出的
    // 但我们可以通过其他方式检查
    console.log('📋 当前使用的API端点: https://api.fuio.tech')
    console.log('📋 预期模型名称: sora-img')
  })
}
