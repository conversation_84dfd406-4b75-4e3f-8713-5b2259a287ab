<template>
  <div class="token-cache-test">
    <div class="test-header">
      <h2>🔐 Token缓存测试</h2>
      <p>测试浏览器中各种Token缓存方式的性能和可靠性</p>
    </div>

    <!-- 当前Token状态 -->
    <el-card class="status-card">
      <template #header>
        <div class="card-header">
          <h3>📊 当前Token状态</h3>
          <el-button @click="refreshStatus" :loading="loading" size="small" type="primary">
            <el-icon><Refresh /></el-icon>
            刷新状态
          </el-button>
        </div>
      </template>

      <div class="status-grid">
        <div class="status-item">
          <span class="status-label">主Token:</span>
          <el-tag :type="currentToken ? 'success' : 'danger'">
            {{ currentToken ? '存在' : '不存在' }}
          </el-tag>
          <span v-if="currentToken" class="token-preview">
            {{ currentToken.substring(0, 20) }}...
          </span>
        </div>
      </div>
    </el-card>

    <!-- 缓存方式测试 -->
    <el-card class="cache-test-card">
      <template #header>
        <h3>🧪 缓存方式测试</h3>
      </template>

      <div class="cache-methods">
        <div v-for="(method, key) in cacheMethods" :key="key" class="cache-method">
          <div class="method-header">
            <h4>{{ method.name }}</h4>
            <div class="method-status">
              <el-tag :type="method.status.available ? 'success' : 'danger'" size="small">
                {{ method.status.available ? '可用' : '不可用' }}
              </el-tag>
              <el-tag v-if="method.status.hasToken" type="info" size="small">
                有Token
              </el-tag>
            </div>
          </div>
          
          <div class="method-description">
            <p><strong>优点:</strong> {{ method.pros }}</p>
            <p><strong>缺点:</strong> {{ method.cons }}</p>
            <p><strong>适用场景:</strong> {{ method.useCase }}</p>
          </div>

          <div class="method-actions">
            <el-button-group>
              <el-button 
                @click="testSave(key)" 
                :loading="method.testing" 
                size="small" 
                type="primary"
              >
                保存测试
              </el-button>
              <el-button 
                @click="testLoad(key)" 
                :loading="method.testing" 
                size="small" 
                type="success"
              >
                读取测试
              </el-button>
              <el-button 
                @click="testRemove(key)" 
                :loading="method.testing" 
                size="small" 
                type="danger"
              >
                删除测试
              </el-button>
            </el-button-group>
          </div>

          <div v-if="method.result" class="method-result">
            <div :class="['result-item', method.result.success ? 'success' : 'error']">
              <el-icon>
                <CircleCheck v-if="method.result.success" />
                <CircleClose v-else />
              </el-icon>
              <span>{{ method.result.message }}</span>
              <span class="result-time">{{ method.result.time }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 缓存管理器测试 -->
    <el-card class="manager-test-card">
      <template #header>
        <h3>⚙️ 缓存管理器测试</h3>
      </template>

      <div class="manager-tests">
        <div class="test-section">
          <h4>默认缓存管理器</h4>
          <p>主缓存: localStorage，备用: sessionStorage, cookie, memory</p>
          <div class="test-buttons">
            <el-button @click="testDefaultManager" :loading="managerTesting.default" type="primary">
              测试默认管理器
            </el-button>
          </div>
        </div>

        <div class="test-section">
          <h4>高可靠性缓存管理器</h4>
          <p>多缓存模式: localStorage + IndexedDB + Cookie</p>
          <div class="test-buttons">
            <el-button @click="testReliableManager" :loading="managerTesting.reliable" type="success">
              测试可靠性管理器
            </el-button>
          </div>
        </div>

        <div class="test-section">
          <h4>会话缓存管理器</h4>
          <p>临时存储: sessionStorage + memory</p>
          <div class="test-buttons">
            <el-button @click="testSessionManager" :loading="managerTesting.session" type="info">
              测试会话管理器
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 性能测试 -->
    <el-card class="performance-card">
      <template #header>
        <h3>⚡ 性能测试</h3>
      </template>

      <div class="performance-tests">
        <el-button @click="runPerformanceTest" :loading="performanceTesting" type="warning" size="large">
          运行性能测试
        </el-button>
        
        <div v-if="performanceResults.length > 0" class="performance-results">
          <el-table :data="performanceResults" style="width: 100%">
            <el-table-column prop="method" label="缓存方式" width="150" />
            <el-table-column prop="saveTime" label="保存耗时(ms)" width="120" />
            <el-table-column prop="loadTime" label="读取耗时(ms)" width="120" />
            <el-table-column prop="removeTime" label="删除耗时(ms)" width="120" />
            <el-table-column prop="reliability" label="可靠性" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.reliability > 90 ? 'success' : scope.row.reliability > 70 ? 'warning' : 'danger'">
                  {{ scope.row.reliability }}%
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>

    <!-- 测试结果日志 -->
    <el-card v-if="testLogs.length > 0" class="logs-card">
      <template #header>
        <div class="card-header">
          <h3>📋 测试日志</h3>
          <el-button @click="clearLogs" size="small">清除日志</el-button>
        </div>
      </template>

      <div class="logs-container">
        <div v-for="(log, index) in testLogs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span :class="['log-level', log.level]">{{ log.level.toUpperCase() }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </el-card>

    <!-- 使用建议 -->
    <el-card class="recommendations-card">
      <template #header>
        <h3>💡 使用建议</h3>
      </template>

      <div class="recommendations">
        <div class="recommendation-item">
          <h4>🎯 生产环境推荐</h4>
          <p>使用 <code>reliableTokenCache</code> (localStorage + IndexedDB + Cookie 多重备份)</p>
          <ul>
            <li>高可靠性，多重备份防止数据丢失</li>
            <li>自动降级，确保在任何情况下都能工作</li>
            <li>适合需要长期保持登录状态的应用</li>
          </ul>
        </div>

        <div class="recommendation-item">
          <h4>🚀 开发环境推荐</h4>
          <p>使用 <code>defaultTokenCache</code> (localStorage + 备用缓存)</p>
          <ul>
            <li>简单快速，便于调试</li>
            <li>支持降级策略</li>
            <li>适合开发和测试阶段</li>
          </ul>
        </div>

        <div class="recommendation-item">
          <h4>🔒 安全性考虑</h4>
          <ul>
            <li>敏感Token建议设置过期时间</li>
            <li>HTTPS环境下使用Cookie的Secure标志</li>
            <li>定期清理过期的缓存数据</li>
            <li>考虑使用Token刷新机制</li>
          </ul>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, CircleCheck, CircleClose } from '@element-plus/icons-vue'
import { 
  localStorageCache, 
  sessionStorageCache, 
  indexedDBCache, 
  cookieCache, 
  memoryCache,
  defaultTokenCache,
  reliableTokenCache,
  sessionTokenCache
} from '@/utils/tokenCacheManager'
import { getToken, getTokenCacheStatus } from '@/utils/auth'

// 响应式数据
const loading = ref(false)
const currentToken = ref('')
const testLogs = ref([])
const performanceTesting = ref(false)
const performanceResults = ref([])

const managerTesting = reactive({
  default: false,
  reliable: false,
  session: false
})

// 缓存方式配置
const cacheMethods = reactive({
  localStorage: {
    name: 'localStorage',
    cache: localStorageCache,
    pros: '持久化存储，关闭浏览器后仍然存在',
    cons: '同域名下所有标签页共享，存储大小限制(5-10MB)',
    useCase: '长期登录状态保持',
    status: { available: true, hasToken: false },
    testing: false,
    result: null
  },
  sessionStorage: {
    name: 'sessionStorage',
    cache: sessionStorageCache,
    pros: '标签页隔离，关闭标签页自动清除',
    cons: '刷新页面会丢失，不适合持久化',
    useCase: '临时会话状态',
    status: { available: true, hasToken: false },
    testing: false,
    result: null
  },
  indexedDB: {
    name: 'IndexedDB',
    cache: indexedDBCache,
    pros: '存储容量大，支持复杂数据结构',
    cons: '异步操作，相对复杂',
    useCase: '大量数据存储',
    status: { available: true, hasToken: false },
    testing: false,
    result: null
  },
  cookie: {
    name: 'Cookie',
    cache: cookieCache,
    pros: '自动发送到服务器，支持过期时间',
    cons: '大小限制(4KB)，安全性相对较低',
    useCase: '需要服务器端验证的场景',
    status: { available: true, hasToken: false },
    testing: false,
    result: null
  },
  memory: {
    name: '内存缓存',
    cache: memoryCache,
    pros: '速度最快，不会持久化',
    cons: '刷新页面丢失，仅适合临时使用',
    useCase: '临时数据缓存',
    status: { available: true, hasToken: false },
    testing: false,
    result: null
  }
})

// 添加日志
const addLog = (level, message) => {
  testLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    level,
    message
  })
  
  // 限制日志数量
  if (testLogs.value.length > 100) {
    testLogs.value = testLogs.value.slice(0, 100)
  }
}

// 生成测试Token
const generateTestToken = () => {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substr(2, 9)
  const expirationTime = timestamp + 24 * 60 * 60 * 1000 // 24小时后过期
  return `test_token_${timestamp}_${random}_exp_${expirationTime}`
}

// 刷新状态
const refreshStatus = async () => {
  loading.value = true
  try {
    // 获取当前Token
    currentToken.value = await getToken() || ''
    
    // 获取各缓存的状态
    const cacheStatus = await getTokenCacheStatus()
    
    // 更新缓存方法状态
    for (const [key, method] of Object.entries(cacheMethods)) {
      if (cacheStatus[key]) {
        method.status = cacheStatus[key]
      } else {
        // 手动检查
        try {
          method.status.hasToken = await method.cache.exists()
        } catch (error) {
          method.status.available = false
          method.status.hasToken = false
        }
      }
    }
    
    addLog('info', '状态刷新完成')
  } catch (error) {
    addLog('error', `状态刷新失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

// 测试保存
const testSave = async (methodKey) => {
  const method = cacheMethods[methodKey]
  method.testing = true
  
  try {
    const testToken = generateTestToken()
    const startTime = performance.now()
    
    const success = await method.cache.set(testToken, {
      expiresAt: Date.now() + 24 * 60 * 60 * 1000
    })
    
    const endTime = performance.now()
    const duration = Math.round(endTime - startTime)
    
    method.result = {
      success,
      message: success ? `保存成功 (${duration}ms)` : '保存失败',
      time: new Date().toLocaleTimeString()
    }
    
    addLog(success ? 'info' : 'error', `${method.name} 保存测试: ${method.result.message}`)
    
    if (success) {
      method.status.hasToken = true
    }
  } catch (error) {
    method.result = {
      success: false,
      message: `保存异常: ${error.message}`,
      time: new Date().toLocaleTimeString()
    }
    addLog('error', `${method.name} 保存异常: ${error.message}`)
  } finally {
    method.testing = false
  }
}

// 测试读取
const testLoad = async (methodKey) => {
  const method = cacheMethods[methodKey]
  method.testing = true
  
  try {
    const startTime = performance.now()
    const token = await method.cache.get()
    const endTime = performance.now()
    const duration = Math.round(endTime - startTime)
    
    const success = !!token
    method.result = {
      success,
      message: success ? `读取成功 (${duration}ms): ${token.substring(0, 20)}...` : `读取失败 (${duration}ms)`,
      time: new Date().toLocaleTimeString()
    }
    
    addLog(success ? 'info' : 'warn', `${method.name} 读取测试: ${method.result.message}`)
  } catch (error) {
    method.result = {
      success: false,
      message: `读取异常: ${error.message}`,
      time: new Date().toLocaleTimeString()
    }
    addLog('error', `${method.name} 读取异常: ${error.message}`)
  } finally {
    method.testing = false
  }
}

// 测试删除
const testRemove = async (methodKey) => {
  const method = cacheMethods[methodKey]
  method.testing = true
  
  try {
    const startTime = performance.now()
    const success = await method.cache.remove()
    const endTime = performance.now()
    const duration = Math.round(endTime - startTime)
    
    method.result = {
      success,
      message: success ? `删除成功 (${duration}ms)` : `删除失败 (${duration}ms)`,
      time: new Date().toLocaleTimeString()
    }
    
    addLog(success ? 'info' : 'error', `${method.name} 删除测试: ${method.result.message}`)
    
    if (success) {
      method.status.hasToken = false
    }
  } catch (error) {
    method.result = {
      success: false,
      message: `删除异常: ${error.message}`,
      time: new Date().toLocaleTimeString()
    }
    addLog('error', `${method.name} 删除异常: ${error.message}`)
  } finally {
    method.testing = false
  }
}

// 测试缓存管理器
const testCacheManager = async (manager, name, loadingKey) => {
  managerTesting[loadingKey] = true
  
  try {
    const testToken = generateTestToken()
    
    // 测试保存
    addLog('info', `开始测试${name}管理器...`)
    const saveSuccess = await manager.setToken(testToken)
    addLog(saveSuccess ? 'info' : 'error', `${name} 保存: ${saveSuccess ? '成功' : '失败'}`)
    
    // 测试读取
    const loadedToken = await manager.getToken()
    const loadSuccess = loadedToken === testToken
    addLog(loadSuccess ? 'info' : 'error', `${name} 读取: ${loadSuccess ? '成功' : '失败'}`)
    
    // 测试状态
    const hasToken = await manager.hasToken()
    addLog('info', `${name} 状态检查: ${hasToken ? '有Token' : '无Token'}`)
    
    // 测试缓存状态
    const cacheStatus = await manager.getCacheStatus()
    addLog('info', `${name} 缓存状态: ${JSON.stringify(cacheStatus)}`)
    
    // 测试删除
    const removeSuccess = await manager.removeToken()
    addLog(removeSuccess ? 'info' : 'error', `${name} 删除: ${removeSuccess ? '成功' : '失败'}`)
    
    ElMessage.success(`${name}管理器测试完成`)
  } catch (error) {
    addLog('error', `${name}管理器测试异常: ${error.message}`)
    ElMessage.error(`${name}管理器测试失败`)
  } finally {
    managerTesting[loadingKey] = false
  }
}

const testDefaultManager = () => testCacheManager(defaultTokenCache, '默认', 'default')
const testReliableManager = () => testCacheManager(reliableTokenCache, '高可靠性', 'reliable')
const testSessionManager = () => testCacheManager(sessionTokenCache, '会话', 'session')

// 性能测试
const runPerformanceTest = async () => {
  performanceTesting.value = true
  performanceResults.value = []
  
  try {
    addLog('info', '开始性能测试...')
    
    for (const [key, method] of Object.entries(cacheMethods)) {
      const testToken = generateTestToken()
      const result = {
        method: method.name,
        saveTime: 0,
        loadTime: 0,
        removeTime: 0,
        reliability: 0
      }
      
      let successCount = 0
      const testCount = 10
      
      // 多次测试取平均值
      for (let i = 0; i < testCount; i++) {
        try {
          // 保存测试
          const saveStart = performance.now()
          const saveSuccess = await method.cache.set(testToken + i)
          const saveEnd = performance.now()
          result.saveTime += (saveEnd - saveStart)
          
          if (saveSuccess) {
            // 读取测试
            const loadStart = performance.now()
            const loadedToken = await method.cache.get()
            const loadEnd = performance.now()
            result.loadTime += (loadEnd - loadStart)
            
            if (loadedToken) {
              // 删除测试
              const removeStart = performance.now()
              await method.cache.remove()
              const removeEnd = performance.now()
              result.removeTime += (removeEnd - removeStart)
              
              successCount++
            }
          }
        } catch (error) {
          // 忽略单次测试错误
        }
      }
      
      // 计算平均值
      result.saveTime = Math.round(result.saveTime / testCount)
      result.loadTime = Math.round(result.loadTime / testCount)
      result.removeTime = Math.round(result.removeTime / testCount)
      result.reliability = Math.round((successCount / testCount) * 100)
      
      performanceResults.value.push(result)
      addLog('info', `${method.name} 性能测试完成: 可靠性 ${result.reliability}%`)
    }
    
    addLog('info', '性能测试完成')
    ElMessage.success('性能测试完成')
  } catch (error) {
    addLog('error', `性能测试异常: ${error.message}`)
    ElMessage.error('性能测试失败')
  } finally {
    performanceTesting.value = false
  }
}

// 清除日志
const clearLogs = () => {
  testLogs.value = []
}

// 生命周期
onMounted(() => {
  refreshStatus()
})
</script>

<style lang="scss" scoped>
.token-cache-test {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
}

.test-header h2 {
  color: #409eff;
  margin-bottom: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-card,
.cache-test-card,
.manager-test-card,
.performance-card,
.logs-card,
.recommendations-card {
  margin-bottom: 20px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-label {
  font-weight: bold;
  min-width: 80px;
}

.token-preview {
  font-family: monospace;
  color: #666;
  font-size: 12px;
}

.cache-methods {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.cache-method {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
}

.method-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.method-header h4 {
  margin: 0;
  color: #409eff;
}

.method-status {
  display: flex;
  gap: 5px;
}

.method-description {
  margin-bottom: 15px;
  font-size: 14px;
  line-height: 1.5;
}

.method-description p {
  margin: 5px 0;
}

.method-actions {
  margin-bottom: 10px;
}

.method-result {
  margin-top: 10px;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 4px;
  font-size: 14px;
}

.result-item.success {
  background-color: #f0f9ff;
  color: #67c23a;
}

.result-item.error {
  background-color: #fef0f0;
  color: #f56c6c;
}

.result-time {
  margin-left: auto;
  font-size: 12px;
  color: #909399;
}

.manager-tests {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.test-section {
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.test-section h4 {
  color: #409eff;
  margin-bottom: 10px;
}

.test-buttons {
  margin-top: 15px;
}

.performance-tests {
  text-align: center;
}

.performance-results {
  margin-top: 20px;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 12px;
}

.log-item {
  display: flex;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-time {
  color: #909399;
  min-width: 80px;
}

.log-level {
  min-width: 50px;
  font-weight: bold;
}

.log-level.info {
  color: #409eff;
}

.log-level.warn {
  color: #e6a23c;
}

.log-level.error {
  color: #f56c6c;
}

.log-message {
  flex: 1;
}

.recommendations {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.recommendation-item {
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.recommendation-item h4 {
  color: #409eff;
  margin-bottom: 10px;
}

.recommendation-item code {
  background-color: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
}

.recommendation-item ul {
  margin: 10px 0;
  padding-left: 20px;
}

.recommendation-item li {
  margin: 5px 0;
}
</style>
