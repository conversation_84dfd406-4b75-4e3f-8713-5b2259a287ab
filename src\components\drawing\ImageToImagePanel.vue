<template>
  <div class="image-to-image-panel">
    <!-- 面板标题 -->
    <div class="panel-header">
      <div class="flex items-center space-x-2">
        <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" class="text-white">
            <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z" fill="currentColor"/>
          </svg>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900">图文生图</h3>
          <p class="text-sm text-gray-500">上传图片并添加描述来编辑图像</p>
        </div>
      </div>
    </div>

    <!-- 图片上传区域 -->
    <div class="upload-section">
      <div class="upload-label">
        <span class="text-sm font-medium text-gray-700">参考图像</span>
        <span class="text-xs text-gray-500 ml-2">支持 JPG、PNG 格式</span>
      </div>
      
      <div 
        class="upload-area"
        :class="{ 'drag-over': isDragOver, 'has-image': uploadedImage }"
        @drop="handleDrop"
        @dragover.prevent="isDragOver = true"
        @dragleave="isDragOver = false"
        @click="triggerFileInput"
      >
        <input
          ref="fileInput"
          type="file"
          accept="image/*"
          @change="handleFileSelect"
          class="hidden"
        />
        
        <div v-if="!uploadedImage" class="upload-placeholder">
          <div class="upload-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" class="text-gray-400">
              <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z" fill="currentColor"/>
            </svg>
          </div>
          <p class="text-gray-600 font-medium">点击或拖拽上传图片</p>
          <p class="text-gray-400 text-sm">最大支持 10MB</p>
        </div>
        
        <div v-else class="uploaded-image">
          <img :src="uploadedImage.preview" :alt="uploadedImage.name" class="preview-image" />
          <div class="image-overlay">
            <button @click.stop="removeImage" class="remove-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" class="text-white">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </button>
          </div>
          <div class="image-info">
            <p class="text-sm font-medium text-gray-700 truncate">{{ uploadedImage.name }}</p>
            <p class="text-xs text-gray-500">{{ formatFileSize(uploadedImage.size) }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 提示词输入 -->
    <div class="prompt-section">
      <label class="block text-sm font-medium text-gray-700 mb-2">
        编辑描述
        <span class="text-red-500">*</span>
      </label>
      <textarea
        v-model="prompt"
        placeholder="描述您想要对图像进行的修改..."
        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
        rows="3"
      ></textarea>
    </div>

    <!-- 生成设置 -->
    <div class="settings-section">
      <div class="setting-row">
        <label class="text-sm font-medium text-gray-700">图片尺寸</label>
        <select v-model="selectedSize" class="setting-select">
          <option v-for="size in supportedSizes" :key="size.ratio" :value="size.ratio">
            {{ size.label }}
          </option>
        </select>
      </div>
      
      <div class="setting-row">
        <label class="text-sm font-medium text-gray-700">生成数量</label>
        <select v-model="imageCount" class="setting-select">
          <option :value="1">1张</option>
          <option :value="2">2张</option>
          <option :value="3">3张</option>
          <option :value="4">4张</option>
        </select>
      </div>
    </div>

    <!-- 生成按钮 -->
    <div class="action-section">
      <button
        @click="generateImage"
        :disabled="!canGenerate || loading"
        class="generate-btn"
        :class="{ 'loading': loading }"
      >
        <div v-if="loading" class="loading-spinner"></div>
        <span>{{ loading ? '生成中...' : '开始编辑' }}</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { getSupportedSizes } from '@/services/fuioApi.js'

// Props
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['generate', 'error'])

// 响应式数据
const fileInput = ref(null)
const uploadedImage = ref(null)
const prompt = ref('')
const selectedSize = ref('1:1')
const imageCount = ref(1)
const isDragOver = ref(false)
const supportedSizes = ref([])

// 计算属性
const canGenerate = computed(() => {
  return uploadedImage.value && prompt.value.trim() && !props.loading
})

// 方法
const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (file) {
    processFile(file)
  }
}

const handleDrop = (event) => {
  event.preventDefault()
  isDragOver.value = false
  
  const files = event.dataTransfer.files
  if (files.length > 0) {
    processFile(files[0])
  }
}

const processFile = (file) => {
  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    emit('error', '请选择图片文件')
    return
  }

  // 验证文件大小 (10MB)
  if (file.size > 10 * 1024 * 1024) {
    emit('error', '图片大小不能超过10MB')
    return
  }
  
  // 创建预览
  const reader = new FileReader()
  reader.onload = (e) => {
    uploadedImage.value = {
      file: file,
      name: file.name,
      size: file.size,
      preview: e.target.result
    }
  }
  reader.readAsDataURL(file)
}

const removeImage = () => {
  uploadedImage.value = null
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const generateImage = () => {
  if (!canGenerate.value) return
  
  emit('generate', {
    prompt: prompt.value.trim(),
    image: uploadedImage.value.file,
    aspectRatio: selectedSize.value,
    n: imageCount.value
  })
}

// 生命周期
onMounted(() => {
  supportedSizes.value = getSupportedSizes()
})
</script>

<style scoped>
.image-to-image-panel {
  @apply space-y-6;
}

.panel-header {
  @apply pb-4 border-b border-gray-200;
}

.upload-section {
  @apply space-y-2;
}

.upload-label {
  @apply flex items-center;
}

.upload-area {
  @apply relative border-2 border-dashed border-gray-300 rounded-lg p-6 cursor-pointer transition-all duration-200;
}

.upload-area:hover {
  @apply border-blue-400 bg-blue-50;
}

.upload-area.drag-over {
  @apply border-blue-500 bg-blue-100;
}

.upload-area.has-image {
  @apply border-solid border-gray-200 p-0;
}

.upload-placeholder {
  @apply text-center;
}

.upload-icon {
  @apply mb-3;
}

.uploaded-image {
  @apply relative;
}

.preview-image {
  @apply w-full h-48 object-cover rounded-lg;
}

.image-overlay {
  @apply absolute top-2 right-2;
}

.remove-btn {
  @apply w-8 h-8 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center transition-colors;
}

.image-info {
  @apply p-3;
}

.prompt-section {
  @apply space-y-2;
}

.settings-section {
  @apply space-y-4;
}

.setting-row {
  @apply flex items-center justify-between;
}

.setting-select {
  @apply px-3 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.action-section {
  @apply pt-4 border-t border-gray-200;
}

.generate-btn {
  @apply w-full px-4 py-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white font-medium rounded-lg hover:from-blue-600 hover:to-purple-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center space-x-2;
}

.loading-spinner {
  @apply w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin;
}
</style>
